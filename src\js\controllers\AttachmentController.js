/**
 * Contrôleur de pièces jointes - Gère les fichiers uploadés
 * Principe Single Responsibility: Responsable uniquement des pièces jointes
 */
class AttachmentController {
    constructor(appController) {
        this.app = appController;
        this.currentAttachment = null;
    }

    /**
     * Gère l'upload d'un fichier
     * @param {Event} event - L'événement de changement de fichier
     */
    handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            // Valider le fichier
            this.validateFile(file);

            // Traiter selon le type
            if (file.type.startsWith('image/')) {
                this.handleImageUpload(file);
            } else if (file.name.endsWith('.html') || file.type === 'text/html') {
                this.handleHtmlUpload(file);
            } else {
                throw new Error('Type de fichier non supporté');
            }

        } catch (error) {
            console.error('❌ Erreur upload:', error);
            alert(`Erreur lors de l'upload: ${error.message}`);
        }

        // Réinitialiser l'input
        event.target.value = '';
    }

    /**
     * Valide un fichier
     * @param {File} file - Le fichier à valider
     */
    validateFile(file) {
        // Vérifier la taille
        if (file.size > CONFIG.MAX_FILE_SIZE) {
            throw new Error(`Fichier trop volumineux (max: ${CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB)`);
        }

        // Vérifier le type
        const isImage = file.type.startsWith('image/');
        const isHtml = file.name.endsWith('.html') || file.type === 'text/html';
        
        if (!isImage && !isHtml) {
            throw new Error('Seuls les images et fichiers HTML sont acceptés');
        }
    }

    /**
     * Gère l'upload d'une image
     * @param {File} file - Le fichier image
     */
    handleImageUpload(file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            this.currentAttachment = {
                type: CONTENT_TYPES.IMAGE,
                data: e.target.result,
                fileName: file.name
            };
            
            this.showAttachmentPreview();
            
            // Émettre un événement
            document.dispatchEvent(new CustomEvent(EVENTS.ATTACHMENT_ADDED, {
                detail: { type: CONTENT_TYPES.IMAGE, fileName: file.name }
            }));
            
            console.log('📎 Image attachée:', file.name);
        };
        
        reader.onerror = () => {
            throw new Error('Erreur lors de la lecture du fichier');
        };
        
        reader.readAsDataURL(file);
    }

    /**
     * Gère l'upload d'un fichier HTML
     * @param {File} file - Le fichier HTML
     */
    handleHtmlUpload(file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            this.currentAttachment = {
                type: CONTENT_TYPES.HTML,
                data: e.target.result,
                fileName: file.name
            };
            
            this.showAttachmentPreview();
            
            // Émettre un événement
            document.dispatchEvent(new CustomEvent(EVENTS.ATTACHMENT_ADDED, {
                detail: { type: CONTENT_TYPES.HTML, fileName: file.name }
            }));
            
            console.log('📎 HTML attaché:', file.name);
        };
        
        reader.onerror = () => {
            throw new Error('Erreur lors de la lecture du fichier');
        };
        
        reader.readAsText(file);
    }

    /**
     * Affiche l'aperçu de la pièce jointe
     */
    showAttachmentPreview() {
        const preview = document.getElementById('attachment-preview');
        const fileName = document.getElementById('attachment-name');
        
        if (!preview || !fileName) {
            console.error('❌ Éléments d\'aperçu non trouvés');
            return;
        }

        if (this.currentAttachment) {
            fileName.textContent = this.currentAttachment.fileName;
            preview.classList.add('visible');
        }
    }

    /**
     * Supprime la pièce jointe actuelle
     */
    removeAttachment() {
        this.currentAttachment = null;
        
        const preview = document.getElementById('attachment-preview');
        if (preview) {
            preview.classList.remove('visible');
        }
        
        // Émettre un événement
        document.dispatchEvent(new CustomEvent(EVENTS.ATTACHMENT_REMOVED));
        
        console.log('🗑️ Pièce jointe supprimée');
    }

    /**
     * Vérifie s'il y a une pièce jointe
     * @returns {boolean}
     */
    hasAttachment() {
        return this.currentAttachment !== null;
    }

    /**
     * Retourne la pièce jointe actuelle
     * @returns {Object|null}
     */
    getCurrentAttachment() {
        return this.currentAttachment;
    }

    /**
     * Définit une pièce jointe
     * @param {Object} attachment - La pièce jointe
     */
    setAttachment(attachment) {
        if (!attachment || !attachment.type || !attachment.data) {
            throw new Error('Pièce jointe invalide');
        }
        
        this.currentAttachment = attachment;
        this.showAttachmentPreview();
    }

    /**
     * Retourne les types de fichiers acceptés
     * @returns {string}
     */
    getAcceptedTypes() {
        return CONFIG.ACCEPTED_FILE_TYPES;
    }

    /**
     * Retourne la taille maximale autorisée
     * @returns {number}
     */
    getMaxFileSize() {
        return CONFIG.MAX_FILE_SIZE;
    }

    /**
     * Formate la taille d'un fichier
     * @param {number} bytes - La taille en bytes
     * @returns {string}
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Valide le type MIME d'un fichier
     * @param {string} mimeType - Le type MIME
     * @returns {boolean}
     */
    isValidMimeType(mimeType) {
        const validTypes = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml',
            'text/html'
        ];
        
        return validTypes.includes(mimeType);
    }

    /**
     * Extrait les métadonnées d'un fichier
     * @param {File} file - Le fichier
     * @returns {Object}
     */
    extractFileMetadata(file) {
        return {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: new Date(file.lastModified),
            formattedSize: this.formatFileSize(file.size)
        };
    }
}
