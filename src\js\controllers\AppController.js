/**
 * Contrôleur principal de l'application
 * Principe Single Responsibility: Gère l'initialisation et la coordination générale
 * Principe Dependency Injection: Reçoit ses dépendances
 */
class AppController {
    constructor() {
        this.state = APP_STATES.IDLE;
        this.chats = [];
        this.currentChatId = null;
        
        // Services
        this.storageService = new StorageService();
        this.webhookService = new WebhookService();
        this.responseProcessor = new ResponseProcessor();
        
        // Contrôleurs
        this.chatController = null;
        this.messageController = null;
        this.attachmentController = null;
        this.uiController = null;
        this.modalController = null;
        
        this.initializeEventListeners();
    }

    /**
     * Initialise l'application
     */
    async initialize() {
        try {
            console.log('🚀 Initialisation de l\'application...');
            
            // Initialiser les contrôleurs
            this.initializeControllers();
            
            // Charger les données
            await this.loadData();
            
            // Initialiser l'interface
            this.initializeUI();
            
            console.log('✅ Application initialisée avec succès');
            this.setState(APP_STATES.IDLE);
            
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation:', error);
            this.setState(APP_STATES.ERROR);
            this.handleInitializationError(error);
        }
    }

    /**
     * Initialise les contrôleurs
     */
    initializeControllers() {
        this.chatController = new ChatController(this);
        this.messageController = new MessageController(this);
        this.attachmentController = new AttachmentController(this);
        this.uiController = new UIController(this);
        this.modalController = new ModalController(this);
    }

    /**
     * Charge les données depuis le stockage
     */
    async loadData() {
        try {
            this.chats = this.storageService.loadChats();
            console.log('📚 Chats chargés:', this.chats.length);
            
            // Convertir les timestamps si nécessaire
            this.chats.forEach(chat => {
                if (typeof chat.timestamp === 'string') {
                    chat.timestamp = new Date(chat.timestamp);
                }
                chat.messages.forEach(message => {
                    if (typeof message.timestamp === 'string') {
                        message.timestamp = new Date(message.timestamp);
                    }
                });
            });
            
        } catch (error) {
            console.error('❌ Erreur chargement données:', error);
            this.chats = [];
        }
    }

    /**
     * Initialise l'interface utilisateur
     */
    initializeUI() {
        // Rendre la liste des chats
        this.chatController.renderChats();

        // Charger le premier chat ou créer un nouveau
        if (this.chats.length > 0) {
            this.chatController.loadChat(this.chats[0].id);
        } else {
            this.chatController.createNewChat();
        }

        // Configurer les événements d'entrée
        this.setupInputEvents();
    }

    /**
     * Configure les événements d'entrée
     */
    setupInputEvents() {
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        if (messageInput) {
            // Auto-resize du textarea
            messageInput.addEventListener('input', (e) => {
                this.autoResizeTextarea(e.target);
            });
            
            // Envoi avec Ctrl+Enter
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    this.messageController.sendMessage();
                }
            });
        }
        
        if (sendButton) {
            sendButton.addEventListener('click', () => {
                this.messageController.sendMessage();
            });
        }
    }

    /**
     * Auto-redimensionne le textarea
     * @param {HTMLTextAreaElement} textarea - Le textarea à redimensionner
     */
    autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        const newHeight = Math.min(textarea.scrollHeight, CONFIG.MAX_MESSAGE_HEIGHT);
        textarea.style.height = newHeight + 'px';
    }

    /**
     * Initialise les écouteurs d'événements globaux
     */
    initializeEventListeners() {
        // Écouteurs d'événements personnalisés
        document.addEventListener(EVENTS.CHAT_CREATED, (e) => {
            console.log('📝 Nouveau chat créé:', e.detail);
        });
        
        document.addEventListener(EVENTS.MESSAGE_SENT, (e) => {
            console.log('📤 Message envoyé:', e.detail);
        });
        
        document.addEventListener(EVENTS.MESSAGE_RECEIVED, (e) => {
            console.log('📥 Message reçu:', e.detail);
        });
        
        // Sauvegarde automatique avant fermeture
        window.addEventListener('beforeunload', () => {
            this.saveData();
        });
        
        // Sauvegarde périodique
        setInterval(() => {
            this.saveData();
        }, 30000); // Toutes les 30 secondes
    }

    /**
     * Sauvegarde les données
     */
    saveData() {
        try {
            const success = this.storageService.saveChats(this.chats);
            if (success) {
                console.log('💾 Données sauvegardées automatiquement');
            }
        } catch (error) {
            console.error('❌ Erreur sauvegarde automatique:', error);
        }
    }

    /**
     * Définit l'état de l'application
     * @param {string} newState - Le nouvel état
     */
    setState(newState) {
        const oldState = this.state;
        this.state = newState;
        
        console.log(`🔄 État changé: ${oldState} → ${newState}`);
        
        // Émettre un événement de changement d'état
        document.dispatchEvent(new CustomEvent('app:stateChanged', {
            detail: { oldState, newState }
        }));
    }

    /**
     * Retourne l'état actuel
     * @returns {string}
     */
    getState() {
        return this.state;
    }

    /**
     * Gère les erreurs d'initialisation
     * @param {Error} error - L'erreur à gérer
     */
    handleInitializationError(error) {
        // Créer un chat par défaut en cas d'erreur
        this.chats = [];
        this.chatController = new ChatController(this);
        this.chatController.createNewChat();
        
        // Afficher un message d'erreur à l'utilisateur
        console.error('Erreur d\'initialisation, mode dégradé activé');
    }

    /**
     * Remet à zéro l'application
     */
    resetApp() {
        if (confirm('⚠️ Cela va supprimer tout l\'historique des conversations. Continuer ?')) {
            try {
                // Vider le stockage
                this.storageService.clearAll();
                
                // Réinitialiser les données
                this.chats = [];
                this.currentChatId = null;
                
                // Créer un nouveau chat
                this.chatController.createNewChat();
                
                // Vider le panneau droit
                this.modalController.clearRightPanel();
                
                console.log('🔄 Application réinitialisée');
                
            } catch (error) {
                console.error('❌ Erreur lors de la réinitialisation:', error);
            }
        }
    }

    /**
     * Retourne le chat actuel
     * @returns {Chat|null}
     */
    getCurrentChat() {
        return this.chats.find(chat => chat.id === this.currentChatId) || null;
    }

    /**
     * Définit le chat actuel
     * @param {string} chatId - L'ID du chat
     */
    setCurrentChat(chatId) {
        // Désactiver l'ancien chat
        if (this.currentChatId) {
            const oldChat = this.getCurrentChat();
            if (oldChat) {
                oldChat.deactivate();
            }
        }
        
        // Activer le nouveau chat
        this.currentChatId = chatId;
        const newChat = this.getCurrentChat();
        if (newChat) {
            newChat.activate();
        }
    }

    /**
     * Ajoute un chat à la liste
     * @param {Chat} chat - Le chat à ajouter
     */
    addChat(chat) {
        if (!(chat instanceof Chat)) {
            throw new Error('Le paramètre doit être une instance de Chat');
        }
        
        this.chats.unshift(chat); // Ajouter au début
        this.saveData();
    }

    /**
     * Supprime un chat
     * @param {string} chatId - L'ID du chat à supprimer
     */
    removeChat(chatId) {
        const index = this.chats.findIndex(chat => chat.id === chatId);
        if (index !== -1) {
            this.chats.splice(index, 1);
            
            // Si c'était le chat actuel, charger un autre
            if (this.currentChatId === chatId) {
                if (this.chats.length > 0) {
                    this.chatController.loadChat(this.chats[0].id);
                } else {
                    this.chatController.createNewChat();
                }
            }
            
            this.saveData();
        }
    }
}
