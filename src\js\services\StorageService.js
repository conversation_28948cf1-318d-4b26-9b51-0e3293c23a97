/**
 * Service de stockage - Gère la persistance des données
 * Principe Single Responsibility: Responsable uniquement du stockage
 * Principe Dependency Inversion: Interface abstraite pour différents types de stockage
 */
class StorageService {
    constructor(storageKey = CONFIG.STORAGE_KEY) {
        this.storageKey = storageKey;
        this.storage = this.getStorageEngine();
    }

    /**
     * Détermine le moteur de stockage disponible
     * @returns {Storage}
     */
    getStorageEngine() {
        try {
            if (typeof localStorage !== 'undefined' && localStorage !== null) {
                // Test si localStorage est accessible
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return localStorage;
            }
        } catch (e) {
            console.warn('localStorage non disponible, utilisation du stockage en mémoire');
        }
        
        // Fallback vers un stockage en mémoire
        return new MemoryStorage();
    }

    /**
     * Sauvegarde les chats
     * @param {Chat[]} chats - Les chats à sauvegarder
     * @returns {boolean} - Succès de l'opération
     */
    saveChats(chats) {
        try {
            if (!Array.isArray(chats)) {
                throw new Error('Les chats doivent être un tableau');
            }

            const serializedChats = chats.map(chat => {
                if (!(chat instanceof Chat)) {
                    throw new Error('Tous les éléments doivent être des instances de Chat');
                }
                return chat.toJSON();
            });

            const dataToStore = {
                chats: serializedChats,
                version: '1.0',
                timestamp: new Date().toISOString()
            };

            this.storage.setItem(this.storageKey, JSON.stringify(dataToStore));
            console.log('💾 Chats sauvegardés:', chats.length);
            return true;

        } catch (error) {
            console.error('❌ Erreur sauvegarde:', error);
            return false;
        }
    }

    /**
     * Charge les chats depuis le stockage
     * @returns {Chat[]} - Les chats chargés
     */
    loadChats() {
        try {
            const stored = this.storage.getItem(this.storageKey);
            
            if (!stored) {
                console.log('📂 Aucune donnée stockée trouvée');
                return [];
            }

            const data = JSON.parse(stored);
            
            // Vérification de la version et migration si nécessaire
            if (this.needsMigration(data)) {
                return this.migrateData(data);
            }

            if (!data.chats || !Array.isArray(data.chats)) {
                console.warn('⚠️ Format de données invalide');
                return [];
            }

            const chats = data.chats.map(chatData => {
                try {
                    return Chat.fromJSON(chatData);
                } catch (error) {
                    console.error('❌ Erreur lors du chargement d\'un chat:', error);
                    return null;
                }
            }).filter(chat => chat !== null);

            console.log('📂 Chats chargés:', chats.length);
            return chats;

        } catch (error) {
            console.error('❌ Erreur chargement:', error);
            return [];
        }
    }

    /**
     * Vérifie si les données nécessitent une migration
     * @param {Object} data - Les données à vérifier
     * @returns {boolean}
     */
    needsMigration(data) {
        // Si c'est l'ancien format (tableau direct)
        return Array.isArray(data) || !data.version;
    }

    /**
     * Migre les anciennes données vers le nouveau format
     * @param {Object|Array} oldData - Les anciennes données
     * @returns {Chat[]}
     */
    migrateData(oldData) {
        console.log('🔄 Migration des données...');
        
        try {
            let chatsData = Array.isArray(oldData) ? oldData : oldData.chats || [];
            
            const migratedChats = chatsData.map(chatData => {
                // Migration des anciens formats
                if (chatData.messages) {
                    chatData.messages = chatData.messages.map(msgData => {
                        // Assurer que les messages ont tous les champs requis
                        return {
                            id: msgData.id || Date.now().toString(),
                            content: msgData.content || '',
                            role: msgData.role || MESSAGE_ROLES.USER,
                            timestamp: msgData.timestamp || new Date().toISOString(),
                            attachment: msgData.attachment || null,
                            isProcessed: msgData.isProcessed || false
                        };
                    });
                }
                
                return Chat.fromJSON(chatData);
            });

            // Sauvegarder au nouveau format
            this.saveChats(migratedChats);
            console.log('✅ Migration terminée');
            
            return migratedChats;
            
        } catch (error) {
            console.error('❌ Erreur migration:', error);
            return [];
        }
    }

    /**
     * Supprime toutes les données stockées
     * @returns {boolean}
     */
    clearAll() {
        try {
            this.storage.removeItem(this.storageKey);
            console.log('🗑️ Données supprimées');
            return true;
        } catch (error) {
            console.error('❌ Erreur suppression:', error);
            return false;
        }
    }

    /**
     * Vérifie si des données existent
     * @returns {boolean}
     */
    hasData() {
        try {
            return this.storage.getItem(this.storageKey) !== null;
        } catch {
            return false;
        }
    }

    /**
     * Retourne la taille des données stockées (approximative)
     * @returns {number}
     */
    getDataSize() {
        try {
            const data = this.storage.getItem(this.storageKey);
            return data ? data.length : 0;
        } catch {
            return 0;
        }
    }

    /**
     * Exporte les données
     * @returns {Object|null}
     */
    exportData() {
        try {
            const stored = this.storage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : null;
        } catch (error) {
            console.error('❌ Erreur export:', error);
            return null;
        }
    }

    /**
     * Importe des données
     * @param {Object} data - Les données à importer
     * @returns {boolean}
     */
    importData(data) {
        try {
            this.storage.setItem(this.storageKey, JSON.stringify(data));
            console.log('📥 Données importées');
            return true;
        } catch (error) {
            console.error('❌ Erreur import:', error);
            return false;
        }
    }
}

/**
 * Stockage en mémoire comme fallback
 */
class MemoryStorage {
    constructor() {
        this.data = new Map();
    }

    setItem(key, value) {
        this.data.set(key, value);
    }

    getItem(key) {
        return this.data.get(key) || null;
    }

    removeItem(key) {
        this.data.delete(key);
    }

    clear() {
        this.data.clear();
    }
}
