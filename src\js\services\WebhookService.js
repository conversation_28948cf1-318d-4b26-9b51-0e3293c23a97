/**
 * Service Webhook - Gère les communications avec le webhook n8n
 * Principe Single Responsibility: Responsable uniquement des appels API
 * Principe Open/Closed: Extensible pour d'autres types de webhooks
 */
class WebhookService {
    constructor() {
        this.baseUrl = CONFIG.WEBHOOK_URL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }

    /**
     * Envoie un message au webhook
     * @param {string} message - Le message à envoyer
     * @param {Object} attachment - Pièce jointe optionnelle
     * @returns {Promise<Object>} - La réponse du webhook
     */
    async sendMessage(message, attachment = null) {
        try {
            console.log('📤 Envoi vers:', this.baseUrl);
            
            const payload = this.buildPayload(message, attachment);
            console.log('📦 Payload:', payload);

            const response = await this.makeRequest(payload);
            const data = await this.processResponse(response);
            
            console.log('📥 Data reçue:', data);
            return data;
            
        } catch (error) {
            console.error('❌ Erreur WebhookService:', error);
            throw this.handleError(error);
        }
    }

    /**
     * Construit le payload pour la requête
     * @param {string} message - Le message
     * @param {Object} attachment - Pièce jointe
     * @returns {Object}
     */
    buildPayload(message, attachment) {
        const payload = { message };
        
        if (attachment) {
            payload.attachment = {
                type: attachment.type,
                fileName: attachment.fileName,
                data: attachment.data
            };
        }
        
        return payload;
    }

    /**
     * Effectue la requête HTTP
     * @param {Object} payload - Les données à envoyer
     * @returns {Promise<Response>}
     */
    async makeRequest(payload) {
        const requestOptions = {
            method: 'POST',
            headers: this.defaultHeaders,
            body: JSON.stringify(payload)
        };

        const response = await fetch(this.baseUrl, requestOptions);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return response;
    }

    /**
     * Traite la réponse du serveur
     * @param {Response} response - La réponse HTTP
     * @returns {Promise<Object>}
     */
    async processResponse(response) {
        console.log('📊 Réponse status:', response.status);
        console.log('📋 Réponse headers:', response.headers);
        
        // Lire d'abord comme texte pour voir le contenu brut
        const responseText = await response.text();
        console.log('📄 Réponse brute (texte):', responseText);
        console.log('📏 Longueur de la réponse:', responseText.length);
        
        if (!responseText || responseText.trim() === '') {
            console.log('⚠️ Réponse vide du serveur');
            return { reply: CONFIG.DEFAULT_MESSAGES.WEBHOOK_SUCCESS };
        }
        
        try {
            return JSON.parse(responseText);
        } catch (jsonError) {
            console.error('❌ Erreur parsing JSON:', jsonError);
            console.log('📄 Contenu qui a échoué:', responseText);
            return { reply: responseText };
        }
    }

    /**
     * Gère les erreurs de requête
     * @param {Error} error - L'erreur à traiter
     * @returns {Error}
     */
    handleError(error) {
        const message = error.message;
        
        if (message.includes('Failed to fetch')) {
            return new Error(CONFIG.ERROR_MESSAGES.FAILED_TO_FETCH);
        } else if (message.includes('CORS')) {
            return new Error(CONFIG.ERROR_MESSAGES.CORS_ERROR);
        } else if (message.includes('404')) {
            return new Error(CONFIG.ERROR_MESSAGES.NOT_FOUND);
        } else if (message.includes('500')) {
            return new Error(CONFIG.ERROR_MESSAGES.SERVER_ERROR);
        }
        
        return error;
    }

    /**
     * Test de connectivité du webhook
     * @returns {Promise<Object>}
     */
    async testConnection() {
        console.log('🧪 Test du webhook...');
        
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: this.defaultHeaders,
                body: JSON.stringify({ message: 'Test de connexion' })
            });
            
            console.log('📡 Réponse brute:', response);
            const data = await response.json();
            console.log('📦 Données reçues:', data);
            
            return {
                success: true,
                status: response.status,
                data: data,
                message: 'Test réussi!'
            };
            
        } catch (error) {
            console.error('❌ Erreur test:', error);
            return {
                success: false,
                error: error.message,
                message: 'Test échoué'
            };
        }
    }

    /**
     * Valide l'URL du webhook
     * @param {string} url - L'URL à valider
     * @returns {boolean}
     */
    static isValidWebhookUrl(url) {
        try {
            new URL(url);
            return url.startsWith('http://') || url.startsWith('https://');
        } catch {
            return false;
        }
    }

    /**
     * Met à jour l'URL du webhook
     * @param {string} newUrl - La nouvelle URL
     */
    setWebhookUrl(newUrl) {
        if (!WebhookService.isValidWebhookUrl(newUrl)) {
            throw new Error('URL de webhook invalide');
        }
        this.baseUrl = newUrl;
    }

    /**
     * Retourne l'URL actuelle du webhook
     * @returns {string}
     */
    getWebhookUrl() {
        return this.baseUrl;
    }
}
