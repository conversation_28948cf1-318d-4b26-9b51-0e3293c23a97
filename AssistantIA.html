<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Chat IA – Webhook n8n</title>
    <style>
        :root {
            --bg-color:#0a0a0a;
            --bg-secondary:#111;
            --text-color:#e6e6e6;
            --text-secondary:#a0a0a0;
            --accent-color:#6366f1;
            --border-color:#333;
            --error:#ef4444;
            --gradient:linear-gradient(135deg,#667eea 0%,#764ba2 100%);
        }
        *{margin:0;padding:0;box-sizing:border-box;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;}
        body{background:var(--bg-color);color:var(--text-color);height:100vh;display:flex;overflow:hidden;}
        /* Sidebar */
        .sidebar{width:280px;background:var(--bg-secondary);border-right:1px solid var(--border-color);transition:transform .3s;flex-direction:column;position:relative;}
        .sidebar-header{display:flex;justify-content:space-between;align-items:center;padding:24px;border-bottom:1px solid var(--border-color);}
        .sidebar-header h2{font-size:18px;font-weight:600;background:var(--gradient);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
        .history-list{flex:1;overflow-y:auto;padding:12px;}
        .history-item{padding:12px 16px;margin-bottom:4px;border-radius:8px;cursor:pointer;font-size:14px;transition:all .2s;}
        .history-item:hover{background:rgba(255,255,255,.05);}
        .history-item.active{background:rgba(99,102,241,.1);border-left:3px solid var(--accent-color);}
        .toggle-sidebar{position:absolute;top:20px;left:100%;background:var(--bg-secondary);border:1px solid var(--border-color);border-left:none;border-radius:0 8px 8px 0;width:24px;height:40px;cursor:pointer;display:flex;align-items:center;justify-content:center;z-index:10;transition:all .3s;}
        .toggle-sidebar:hover{background:var(--accent-color);color:#fff;}
        /* Main */
        .main{flex:1;display:flex;flex-direction:column;overflow:hidden;}
        .chat-container{flex:1;display:flex;flex-direction:column;padding:20px;overflow:hidden;}
        .chat-header{text-align:center;padding:20px 0;border-bottom:1px solid var(--border-color);margin-bottom:20px;}
        .chat-header h1{font-size:28px;background:var(--gradient);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
        .chat-header p{color:var(--text-secondary);font-size:14px;}
        .messages{flex:1;overflow-y:auto;margin-bottom:20px;scroll-behavior:smooth;}
        .message{margin-bottom:20px;display:flex;gap:12px;animation:fadeIn .3s;}
        .message.user{flex-direction:row-reverse;}
        .message-avatar{width:36px;height:36px;border-radius:8px;background:var(--bg-secondary);display:flex;align-items:center;justify-content:center;font-size:18px;flex-shrink:0;}
        .message.user .message-avatar{background:var(--accent-color);}
        .message-content{max-width:70%;padding:16px;border-radius:12px;background:var(--bg-secondary);line-height:1.6;word-wrap:break-word;}
        .message.user .message-content{background:rgba(99,102,241,.1);border:1px solid rgba(99,102,241,.2);}
        .message-content pre{background:rgba(0,0,0,.3);padding:12px;border-radius:8px;overflow-x:auto;font-family:Consolas,Monaco,monospace;font-size:13px;margin:8px 0;}
        .message-content code{background:rgba(0,0,0,.3);padding:2px 6px;border-radius:4px;font-family:Consolas,Monaco,monospace;font-size:13px;}
        /* Input */
        .input-container{position:relative;margin-top:auto;}
        .attachment-preview{display:none;padding:12px;background:rgba(99,102,241,.1);border:1px solid rgba(99,102,241,.2);border-radius:8px;margin-bottom:12px;font-size:14px;}
        .attachment-preview .file-name{color:var(--accent-color);font-weight:500;}
        .attachment-preview .remove-attachment{float:right;cursor:pointer;color:var(--text-secondary);}
        .attachment-preview .remove-attachment:hover{color:var(--error);}
        .input-wrapper{position:relative;background:var(--bg-secondary);border:1px solid var(--border-color);border-radius:12px;padding:4px;}
        .input-wrapper:focus-within{border-color:var(--accent-color);}
        .message-input{width:100%;background:transparent;border:none;padding:16px 52px 16px 16px;color:var(--text-color);font-size:16px;resize:none;outline:none;max-height:120px;min-height:52px;font-family:inherit;}
        .message-input::placeholder{color:var(--text-secondary);}
        .file-input{display:none;}
        .attachment-button,.send-button{position:absolute;bottom:8px;border:none;width:36px;height:36px;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s;color:#fff;}
        .attachment-button{right:52px;background:transparent;color:var(--text-secondary);}
        .attachment-button:hover{color:var(--accent-color);transform:scale(1.1);}
        .send-button{right:8px;background:var(--accent-color);}
        .send-button:hover{background:#4f46e5;transform:scale(1.05);}
        .send-button:disabled{opacity:.5;cursor:not-allowed;transform:none;}
        /* Right panel */
        .right-panel{width:320px;background:var(--bg-secondary);border-left:1px solid var(--border-color);display:flex;flex-direction:column;transition:transform .3s;position:relative;}
        .right-panel-header{display:flex;justify-content:space-between;align-items:center;padding:24px;border-bottom:1px solid var(--border-color);}
        .right-panel-header h2{font-size:18px;font-weight:600;}
        .panel-content{flex:1;padding:16px;overflow-y:auto;display:flex;flex-direction:column;gap:16px;}
        .panel-section{background:rgba(255,255,255,.02);border:1px solid var(--border-color);border-radius:12px;padding:16px;}
        .panel-section h3{margin-bottom:12px;font-size:14px;color:var(--text-secondary);font-weight:600;}
        .image-preview{width:100%;border-radius:8px;margin-bottom:8px;cursor:pointer;transition:all .3s;}
        .image-preview:hover{transform:scale(1.02);}
        .html-preview{border:1px solid var(--border-color);border-radius:8px;padding:12px;background:rgba(0,0,0,.2);max-height:200px;overflow-y:auto;font-family:Consolas,Monaco,monospace;font-size:13px;line-height:1.5;}
        .toggle-right-panel{position:absolute;top:20px;right:100%;background:var(--bg-secondary);border:1px solid var(--border-color);border-right:none;border-radius:8px 0 0 8px;width:24px;height:40px;cursor:pointer;display:flex;align-items:center;justify-content:center;z-index:10;transition:all .3s;}
        .toggle-right-panel:hover{background:var(--accent-color);color:#fff;}
        /* Utilitaires */
        .hidden{transform:translateX(-100%);}
        .hidden-right{transform:translateX(100%);}
        .typing-indicator{display:none;padding:12px 16px;margin-bottom:20px;align-items:center;gap:8px;background:var(--bg-secondary);border-radius:12px;width:fit-content;animation:fadeIn .3s;}
        .typing-dots{display:flex;gap:4px;}
        .typing-dots span{width:8px;height:8px;background:var(--text-secondary);border-radius:50%;animation:typing 1.4s infinite;}
        .typing-dots span:nth-child(2){animation-delay:.2s;}
        .typing-dots span:nth-child(3){animation-delay:.4s;}
        @keyframes typing{0%,60%,100%{transform:translateY(0);opacity:.7;}30%{transform:translateY(-10px);opacity:1;}}
        @keyframes fadeIn{from{opacity:0;transform:translateY(10px);}to{opacity:1;transform:translateY(0);}}
        /* Responsive */
        @media(max-width:768px){
            .sidebar,.right-panel{position:absolute;height:100%;z-index:100;}
        }
    </style>
</head>
<body>

<!-- Sidebar gauche -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <h2>Historique</h2>
        <button class="new-chat-btn" onclick="newChat()" style="background:var(--accent-color);color:#fff;border:none;padding:6px 10px;border-radius:6px;font-size:12px;cursor:pointer;">+ Nouveau</button>
    </div>
    <div class="history-list" id="history-list"></div>
    <div class="toggle-sidebar" onclick="toggleSidebar()">
        <svg width="16" height="16" fill="none" stroke="#fff" stroke-width="2"><polyline points="15 18 9 12 15 6"/></svg>
    </div>
</div>

<!-- Zone centrale -->
<div class="main">
    <div class="chat-container">
        <div class="chat-header">
            <h1>Assistant IA</h1>
            <p>Connexion directe au webhook</p>
        </div>
        <div class="messages" id="messages"></div>
        <div class="typing-indicator" id="typing-indicator">
            <div class="message-avatar">🤖</div>
            <div class="typing-dots"><span></span><span></span><span></span></div>
        </div>
        <div class="input-container">
            <div class="attachment-preview" id="attachment-preview">
                <span class="file-name" id="attachment-name"></span>
                <span class="remove-attachment" onclick="removeAttachment()">×</span>
            </div>
            <div class="input-wrapper">
                <textarea id="message-input" class="message-input" placeholder="Tapez votre message…" rows="1"></textarea>
                <input type="file" id="file-input" class="file-input" accept="image/*,.html" onchange="handleFileUpload(event)">
                <button class="attachment-button" onclick="document.getElementById('file-input').click()">📎</button>
                <button class="send-button" id="send-button" onclick="sendMessage()">
                    <svg width="20" height="20" fill="none" stroke="#fff" stroke-width="2"><line x1="22" y1="2" x2="11" y2="13"/><polygon points="22 2 15 22 11 13 2 9 22 2"/></svg>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Panel droit -->
<div class="right-panel" id="right-panel">
    <div class="right-panel-header"><h2>Visualisation</h2></div>
    <div class="panel-content" id="panel-content">
        <div class="panel-section">
            <h3>Contenu réponse</h3>
            <p style="font-size:14px;color:var(--text-secondary);">Les contenus retournés par le webhook seront affichés ici.</p>
        </div>
    </div>
    <div class="toggle-right-panel" onclick="toggleRightPanel()">
        <svg width="16" height="16" fill="none" stroke="#fff" stroke-width="2"><polyline points="9 18 15 12 9 6"/></svg>
    </div>
</div>

<script>
/* =========  Configuration  ========= */
const WEBHOOK_URL = 'https://n8n.arkeup.com/webhook-test/analyse';

/* =========  Gestion des chats  ========= */
let currentChatId = null;
let chats = JSON.parse(localStorage.getItem('ai-chat-chats') || '[]');
let currentAttachment = null;

/* ---------- Initialisation ---------- */
document.addEventListener('DOMContentLoaded', () => {
    chats.forEach(c => c.timestamp = new Date(c.timestamp));
    renderChats();
    if (chats.length) loadChat(chats[0].id); else newChat();
    setupInputEvents();
});

/* ---------- Historique local ---------- */
function newChat() {
    const chat = {id:Date.now().toString(),title:'Nouvelle conversation',messages:[],timestamp:new Date()};
    chats.unshift(chat);
    loadChat(chat.id);
}
function loadChat(id) {
    currentChatId = id;
    renderChats();
    renderMessages();
    clearRightPanel();
}
function renderChats() {
    const list = document.getElementById('history-list');
    list.innerHTML = '';
    chats.forEach(c => {
        const div = document.createElement('div');
        div.className = 'history-item' + (c.id === currentChatId ? ' active':'');
        div.onclick = () => loadChat(c.id);
        div.innerHTML = `<div>${c.title}</div><div class="timestamp">${c.timestamp.toLocaleString('fr-FR')}</div>`;
        list.appendChild(div);
    });
}
function saveChats() { localStorage.setItem('ai-chat-chats', JSON.stringify(chats)); }

/* ---------- Événements clavier ---------- */
function setupInputEvents() {
    const input = document.getElementById('message-input');
    input.addEventListener('keydown', e => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); sendMessage(); } });
    input.addEventListener('input', () => { input.style.height='auto'; input.style.height=Math.min(input.scrollHeight,120)+'px'; });
}

/* ---------- Envoi au webhook (proxy CORS) ---------- */
async function sendMessage() {
    const input = document.getElementById('message-input');
    const text = input.value.trim();
    if (!text && !currentAttachment) return;

    const userMsg = {id:Date.now().toString(),role:'user',content:text,timestamp:new Date(),attachment:currentAttachment};
    const chat = chats.find(c => c.id === currentChatId);
    if (!chat) return;
    if (!chat.messages.length) chat.title = text.slice(0,50) + (text.length>50?'…':'');
    chat.messages.push(userMsg);
    renderMessages();
    showTypingIndicator();
    input.value='';
    currentAttachment=null;
    document.getElementById('attachment-preview').style.display='none';
    document.getElementById('file-input').value='';

    /* Construction du payload */
    const payload = { message: text };
    if (userMsg.attachment) {
        payload.attachment = {
            type: userMsg.attachment.type,
            fileName: userMsg.attachment.fileName,
            data: userMsg.attachment.data
        };
    }

    try {
        const res = await fetch(WEBHOOK_URL, {
            method: 'POST',
            mode: 'cors',
            headers: {
                'Content-Type':'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(payload)
        });
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        const data = await res.json();
        
        // Gestion automatique de la réponse
        let aiMsg = {
            id: (Date.now()+1).toString(),
            role: 'assistant',
            content: data.reply || '',
            timestamp: new Date(),
            attachment: null
        };

        // Détection du type de réponse
        if (data.attachment) {
            aiMsg.attachment = data.attachment;
            displayInRightPanel(data.attachment);
        } else if (data.image) {
            aiMsg.attachment = {
                type: 'image',
                data: data.image,
                fileName: 'image_from_webhook.png'
            };
            displayInRightPanel(aiMsg.attachment);
        } else if (data.html) {
            aiMsg.attachment = {
                type: 'html',
                data: data.html,
                fileName: 'html_from_webhook.html'
            };
            displayInRightPanel(aiMsg.attachment);
        } else if (isImageURL(aiMsg.content)) {
            aiMsg.attachment = {
                type: 'image',
                data: aiMsg.content,
                fileName: 'webhook_image'
            };
            displayInRightPanel(aiMsg.attachment);
        } else if (isHTML(aiMsg.content)) {
            aiMsg.attachment = {
                type: 'html',
                data: aiMsg.content,
                fileName: 'webhook_html'
            };
            displayInRightPanel(aiMsg.attachment);
        }

        chat.messages.push(aiMsg);
        renderMessages();
    } catch (err) {
        chat.messages.push({id:Date.now().toString(),role:'assistant',content:`⚠️ Erreur : ${err.message}`,timestamp:new Date()});
        renderMessages();
    }
    hideTypingIndicator();
    saveChats();
}

/* ---------- Détection automatique ---------- */
function isImageURL(str) {
    return str.match(/^https?:\/\/.+\.(jpg|jpeg|png|gif|svg|webp)(\?.*)?$/i);
}
function isHTML(str) {
    return str.trim().startsWith('<') && str.trim().endsWith('>');
}

/* ---------- Affichage des messages ---------- */
function renderMessages() {
    const container = document.getElementById('messages');
    container.innerHTML = '';
    const chat = chats.find(c=>c.id===currentChatId);
    if(!chat)return;
    chat.messages.forEach(m=>{
        const div=document.createElement('div');
        div.className='message '+m.role;
        const avatar=m.role==='user'?'👤':'🤖';
        div.innerHTML=`<div class="message-avatar">${avatar}</div><div class="message-content">${formatContent(m.content)}</div>`;
        container.appendChild(div);
        if(m.attachment){
            const att=document.createElement('div');
            att.style.marginTop='8px';
            if(m.attachment.type==='image'){
                att.innerHTML=`<img src="${m.attachment.data}" style="max-width:200px;border-radius:8px;cursor:pointer" onclick='displayInRightPanel(${JSON.stringify(m.attachment)})'>`;
            }else if(m.attachment.type==='html'){
                att.innerHTML=`<div style="padding:8px;background:rgba(0,0,0,.2);border-radius:4px;font-size:12px;cursor:pointer" onclick='displayInRightPanel(${JSON.stringify(m.attachment)})'>📄 Code HTML</div>`;
            }
            div.querySelector('.message-content').appendChild(att);
        }
    });
    container.scrollTop=container.scrollHeight;
}
function formatContent(t){
    return t.replace(/\n/g,'<br>')
            .replace(/(https?:\/\/[^\s]+)/g,'<a href="$1" target="_blank" style="color:var(--accent-color)">$1</a>')
            .replace(/```([\s\S]*?)```/g,'<pre>$1</pre>')
            .replace(/`([^`]+)`/g,'<code>$1</code>');
}

/* ---------- Pièces jointes ---------- */
function handleFileUpload(e){
    const file=e.target.files[0];
    if(!file)return;
    const reader=new FileReader();
    reader.onload=ev=>{
        currentAttachment={type:file.type.startsWith('image/')?'image':'html',fileName:file.name,data:ev.target.result};
        document.getElementById('attachment-name').textContent=file.name;
        document.getElementById('attachment-preview').style.display='block';
    };
    if(file.type.startsWith('image/')) reader.readAsDataURL(file);
    else if(file.type==='text/html'||file.name.endsWith('.html')) reader.readAsText(file);
}
function removeAttachment(){
    currentAttachment=null;
    document.getElementById('attachment-preview').style.display='none';
    document.getElementById('file-input').value='';
}

/* ---------- Panel droit ---------- */
function displayInRightPanel(att){
    const pc=document.getElementById('panel-content');
    pc.innerHTML='';
    if(att.type==='image'){
        pc.innerHTML=`<div class="panel-section"><h3>Image</h3><img class="image-preview" src="${att.data}"><p style="font-size:14px;color:var(--text-secondary)">${att.fileName}</p></div>`;
    }else if(att.type==='html'){
        pc.innerHTML=`<div class="panel-section"><h3>HTML</h3><div class="html-preview">${escapeHtml(att.data)}</div><div style="border:1px solid var(--border-color);border-radius:8px;padding:12px;margin-top:12px;">${att.data}</div></div>`;
    }
}
function clearRightPanel(){ displayInRightPanel({type:'html',data:'',fileName:''}); }
function escapeHtml(t){const d=document.createElement('div');d.textContent=t;return d.innerHTML;}

/* ---------- Indicateur & panels ---------- */
function showTypingIndicator(){document.getElementById('typing-indicator').style.display='flex';}
function hideTypingIndicator(){document.getElementById('typing-indicator').style.display='none';}
function toggleSidebar(){document.getElementById('sidebar').classList.toggle('hidden');}
function toggleRightPanel(){document.getElementById('right-panel').classList.toggle('hidden-right');}
</script>
</body>
</html>