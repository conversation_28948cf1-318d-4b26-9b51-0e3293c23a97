<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Chat IA – Webhook n8n</title>
    <style>
        :root {
            --bg-color:#0a0a0a;
            --bg-secondary:#111;
            --text-color:#e6e6e6;
            --text-secondary:#a0a0a0;
            --accent-color:#6366f1;
            --border-color:#333;
            --error:#ef4444;
            --gradient:linear-gradient(135deg,#667eea 0%,#764ba2 100%);
        }
        *{margin:0;padding:0;box-sizing:border-box;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;}
        body{background:var(--bg-color);color:var(--text-color);height:100vh;display:flex;overflow:hidden;}
        /* Sidebar */
        .sidebar{width:280px;background:var(--bg-secondary);border-right:1px solid var(--border-color);transition:transform .3s;flex-direction:column;position:relative;}
        .sidebar-header{display:flex;justify-content:space-between;align-items:center;padding:24px;border-bottom:1px solid var(--border-color);}
        .sidebar-header h2{font-size:18px;font-weight:600;background:var(--gradient);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
        .history-list{flex:1;overflow-y:auto;padding:12px;}
        .history-item{padding:12px 16px;margin-bottom:4px;border-radius:8px;cursor:pointer;font-size:14px;transition:all .2s;}
        .history-item:hover{background:rgba(255,255,255,.05);}
        .history-item.active{background:rgba(99,102,241,.1);border-left:3px solid var(--accent-color);}
        .toggle-sidebar{position:absolute;top:20px;left:100%;background:var(--bg-secondary);border:1px solid var(--border-color);border-left:none;border-radius:0 8px 8px 0;width:24px;height:40px;cursor:pointer;display:flex;align-items:center;justify-content:center;z-index:10;transition:all .3s;}
        .toggle-sidebar:hover{background:var(--accent-color);color:#fff;}
        /* Main */
        .main{flex:1;display:flex;flex-direction:column;overflow:hidden;}
        .chat-container{flex:1;display:flex;flex-direction:column;padding:20px;overflow:hidden;}
        .chat-header{text-align:center;padding:20px 0;border-bottom:1px solid var(--border-color);margin-bottom:20px;}
        .chat-header h1{font-size:28px;background:var(--gradient);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
        .chat-header p{color:var(--text-secondary);font-size:14px;}
        .messages{flex:1;overflow-y:auto;margin-bottom:20px;scroll-behavior:smooth;}
        .message{margin-bottom:20px;display:flex;gap:12px;animation:fadeIn .3s;}
        .message.user{flex-direction:row-reverse;}
        .message-avatar{width:36px;height:36px;border-radius:8px;background:var(--bg-secondary);display:flex;align-items:center;justify-content:center;font-size:18px;flex-shrink:0;}
        .message.user .message-avatar{background:var(--accent-color);}
        .message-content{max-width:70%;padding:16px;border-radius:12px;background:var(--bg-secondary);line-height:1.6;word-wrap:break-word;}
        .message.user .message-content{background:rgba(99,102,241,.1);border:1px solid rgba(99,102,241,.2);}
        .message-content pre{background:rgba(0,0,0,.3);padding:12px;border-radius:8px;overflow-x:auto;font-family:Consolas,Monaco,monospace;font-size:13px;margin:8px 0;border-left:3px solid var(--accent-color);}
        .message-content code{background:rgba(0,0,0,.3);padding:2px 6px;border-radius:4px;font-family:Consolas,Monaco,monospace;font-size:13px;}
        .message-content strong{color:var(--accent-color);font-weight:600;}
        .message-content em{color:var(--text-secondary);font-style:italic;}
        .json-content{background:rgba(0,0,0,.2);padding:12px;border-radius:8px;font-family:Consolas,Monaco,monospace;font-size:13px;border-left:3px solid #10b981;margin:8px 0;}
        .error-content{background:rgba(239,68,68,.1);border:1px solid rgba(239,68,68,.3);padding:12px;border-radius:8px;color:#fca5a5;}
        /* Input */
        .input-container{position:relative;margin-top:auto;}
        .attachment-preview{display:none;padding:12px;background:rgba(99,102,241,.1);border:1px solid rgba(99,102,241,.2);border-radius:8px;margin-bottom:12px;font-size:14px;}
        .attachment-preview .file-name{color:var(--accent-color);font-weight:500;}
        .attachment-preview .remove-attachment{float:right;cursor:pointer;color:var(--text-secondary);}
        .attachment-preview .remove-attachment:hover{color:var(--error);}
        .input-wrapper{position:relative;background:var(--bg-secondary);border:1px solid var(--border-color);border-radius:12px;padding:4px;}
        .input-wrapper:focus-within{border-color:var(--accent-color);}
        .message-input{width:100%;background:transparent;border:none;padding:16px 52px 16px 16px;color:var(--text-color);font-size:16px;resize:none;outline:none;max-height:120px;min-height:52px;font-family:inherit;}
        .message-input::placeholder{color:var(--text-secondary);}
        .file-input{display:none;}
        .attachment-button,.send-button{position:absolute;bottom:8px;border:none;width:36px;height:36px;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s;color:#fff;}
        .attachment-button{right:52px;background:transparent;color:var(--text-secondary);}
        .attachment-button:hover{color:var(--accent-color);transform:scale(1.1);}
        .send-button{right:8px;background:var(--accent-color);}
        .send-button:hover{background:#4f46e5;transform:scale(1.05);}
        .send-button:disabled{opacity:.5;cursor:not-allowed;transform:none;}
        /* Right panel */
        .right-panel{width:320px;background:var(--bg-secondary);border-left:1px solid var(--border-color);display:flex;flex-direction:column;transition:transform .3s;position:relative;}
        .right-panel-header{display:flex;justify-content:space-between;align-items:center;padding:24px;border-bottom:1px solid var(--border-color);}
        .right-panel-header h2{font-size:18px;font-weight:600;}
        .panel-content{flex:1;padding:16px;overflow-y:auto;display:flex;flex-direction:column;gap:16px;}
        .panel-section{background:rgba(255,255,255,.02);border:1px solid var(--border-color);border-radius:12px;padding:16px;}
        .panel-section h3{margin-bottom:12px;font-size:14px;color:var(--text-secondary);font-weight:600;}
        .image-preview{width:100%;border-radius:8px;margin-bottom:8px;cursor:pointer;transition:all .3s;}
        .image-preview:hover{transform:scale(1.02);}
        .html-preview{border:1px solid var(--border-color);border-radius:8px;padding:12px;background:rgba(0,0,0,.2);max-height:200px;overflow-y:auto;font-family:Consolas,Monaco,monospace;font-size:13px;line-height:1.5;}
        .toggle-right-panel{position:absolute;top:20px;right:100%;background:var(--bg-secondary);border:1px solid var(--border-color);border-right:none;border-radius:8px 0 0 8px;width:24px;height:40px;cursor:pointer;display:flex;align-items:center;justify-content:center;z-index:10;transition:all .3s;}
        .toggle-right-panel:hover{background:var(--accent-color);color:#fff;}
        /* Utilitaires */
        .hidden{transform:translateX(-100%);}
        .hidden-right{transform:translateX(100%);}
        .typing-indicator{display:none;padding:12px 16px;margin-bottom:20px;align-items:center;gap:8px;background:var(--bg-secondary);border-radius:12px;width:fit-content;animation:fadeIn .3s;}
        .typing-dots{display:flex;gap:4px;}
        .typing-dots span{width:8px;height:8px;background:var(--text-secondary);border-radius:50%;animation:typing 1.4s infinite;}
        .typing-dots span:nth-child(2){animation-delay:.2s;}
        .typing-dots span:nth-child(3){animation-delay:.4s;}
        @keyframes typing{0%,60%,100%{transform:translateY(0);opacity:.7;}30%{transform:translateY(-10px);opacity:1;}}
        @keyframes fadeIn{from{opacity:0;transform:translateY(10px);}to{opacity:1;transform:translateY(0);}}
        /* Responsive */
        @media(max-width:768px){
            .sidebar,.right-panel{position:absolute;height:100%;z-index:100;}
        }
    </style>
</head>
<body>

<!-- Sidebar gauche -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <h2>Historique</h2>
        <div style="display:flex;gap:4px;flex-wrap:wrap;">
            <button class="new-chat-btn" onclick="newChat()" style="background:var(--accent-color);color:#fff;border:none;padding:6px 10px;border-radius:6px;font-size:12px;cursor:pointer;">+ Nouveau</button>
            <button onclick="resetApp()" style="background:var(--error);color:#fff;border:none;padding:6px 8px;border-radius:6px;font-size:12px;cursor:pointer;" title="Reset complet">🔄</button>
            <button onclick="testWebhook()" style="background:#10b981;color:#fff;border:none;padding:6px 8px;border-radius:6px;font-size:12px;cursor:pointer;" title="Test webhook">🧪</button>
        </div>
    </div>
    <div class="history-list" id="history-list"></div>
    <div class="toggle-sidebar" onclick="toggleSidebar()">
        <svg width="16" height="16" fill="none" stroke="#fff" stroke-width="2"><polyline points="15 18 9 12 15 6"/></svg>
    </div>
</div>

<!-- Zone centrale -->
<div class="main">
    <div class="chat-container">
        <div class="chat-header">
            <h1>Assistant IA</h1>
            <p>Via proxy local optimisé</p>
        </div>
        <div class="messages" id="messages"></div>
        <div class="typing-indicator" id="typing-indicator">
            <div class="message-avatar">🤖</div>
            <div class="typing-dots"><span></span><span></span><span></span></div>
        </div>
        <div class="input-container">
            <div class="attachment-preview" id="attachment-preview">
                <span class="file-name" id="attachment-name"></span>
                <span class="remove-attachment" onclick="removeAttachment()">×</span>
            </div>
            <div class="input-wrapper">
                <textarea id="message-input" class="message-input" placeholder="Tapez votre message…" rows="1"></textarea>
                <input type="file" id="file-input" class="file-input" accept="image/*,.html" onchange="handleFileUpload(event)">
                <button class="attachment-button" onclick="document.getElementById('file-input').click()">📎</button>
                <button class="send-button" id="send-button" onclick="sendMessage()">
                    <svg width="20" height="20" fill="none" stroke="#fff" stroke-width="2"><line x1="22" y1="2" x2="11" y2="13"/><polygon points="22 2 15 22 11 13 2 9 22 2"/></svg>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Panel droit -->
<div class="right-panel" id="right-panel">
    <div class="right-panel-header"><h2>Visualisation</h2></div>
    <div class="panel-content" id="panel-content">
        <div class="panel-section">
            <h3>Contenu réponse</h3>
            <p style="font-size:14px;color:var(--text-secondary);">Les contenus retournés par le webhook seront affichés ici.</p>
        </div>
    </div>
    <div class="toggle-right-panel" onclick="toggleRightPanel()">
        <svg width="16" height="16" fill="none" stroke="#fff" stroke-width="2"><polyline points="9 18 15 12 9 6"/></svg>
    </div>
</div>

<script>
/* =========  Configuration  ========= */
const WEBHOOK_URL = 'http://localhost:3001/webhook-proxy/analyse';

/* =========  Gestion des chats  ========= */
let currentChatId = null;
let chats = JSON.parse(localStorage.getItem('ai-chat-chats') || '[]');
let currentAttachment = null;

/* ---------- Initialisation ---------- */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Application démarrée');
    try {
        chats.forEach(c => c.timestamp = new Date(c.timestamp));
        console.log('📚 Chats chargés:', chats.length);
        renderChats();
        if (chats.length) {
            loadChat(chats[0].id);
        } else {
            newChat();
        }
        setupInputEvents();
        console.log('✅ Initialisation terminée');
    } catch (error) {
        console.error('❌ Erreur lors de l\'initialisation:', error);
        // Créer un chat par défaut en cas d'erreur
        chats = [];
        newChat();
    }
});

/* ---------- Historique local ---------- */
function newChat() {
    const chat = {id:Date.now().toString(),title:'Nouvelle conversation',messages:[],timestamp:new Date()};
    chats.unshift(chat);
    loadChat(chat.id);
}
function loadChat(id) {
    currentChatId = id;
    renderChats();
    renderMessages();
    clearRightPanel();
}
function renderChats() {
    const list = document.getElementById('history-list');
    list.innerHTML = '';
    chats.forEach(c => {
        const div = document.createElement('div');
        div.className = 'history-item' + (c.id === currentChatId ? ' active':'');
        div.onclick = () => loadChat(c.id);
        div.innerHTML = `<div>${c.title}</div><div class="timestamp">${c.timestamp.toLocaleString('fr-FR')}</div>`;
        list.appendChild(div);
    });
}
function saveChats() { localStorage.setItem('ai-chat-chats', JSON.stringify(chats)); }

/* ---------- Événements clavier ---------- */
function setupInputEvents() {
    const input = document.getElementById('message-input');
    input.addEventListener('keydown', e => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); sendMessage(); } });
    input.addEventListener('input', () => { input.style.height='auto'; input.style.height=Math.min(input.scrollHeight,120)+'px'; });
}

/* ---------- Envoi au webhook (proxy CORS) ---------- */
async function sendMessage() {
    const input = document.getElementById('message-input');
    const text = input.value.trim();
    if (!text && !currentAttachment) return;

    const userMsg = {id:Date.now().toString(),role:'user',content:text,timestamp:new Date(),attachment:currentAttachment};
    const chat = chats.find(c => c.id === currentChatId);
    if (!chat) return;
    if (!chat.messages.length) chat.title = text.slice(0,50) + (text.length>50?'…':'');
    chat.messages.push(userMsg);
    renderMessages();
    showTypingIndicator();
    input.value='';
    currentAttachment=null;
    document.getElementById('attachment-preview').style.display='none';
    document.getElementById('file-input').value='';

    /* Construction du payload */
    const payload = { message: text };
    if (userMsg.attachment) {
        payload.attachment = {
            type: userMsg.attachment.type,
            fileName: userMsg.attachment.fileName,
            data: userMsg.attachment.data
        };
    }

    try {
        console.log('Envoi vers:', WEBHOOK_URL);
        console.log('Payload:', payload);

        const res = await fetch(WEBHOOK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(payload)
        });

        console.log('Réponse status:', res.status);
        console.log('Réponse headers:', res.headers);

        if (!res.ok) {
            const errorText = await res.text();
            console.error('Erreur réponse:', errorText);
            throw new Error(`HTTP ${res.status}: ${errorText || res.statusText}`);
        }

        // Lire d'abord comme texte pour voir ce qu'on reçoit vraiment
        const responseText = await res.text();
        console.log('📄 Réponse brute (texte):', responseText);
        console.log('📏 Longueur de la réponse:', responseText.length);

        if (!responseText || responseText.trim() === '') {
            console.log('⚠️ Réponse vide du serveur');
            data = { reply: 'Webhook exécuté avec succès (réponse vide)' };
        } else {
            try {
                data = JSON.parse(responseText);
                console.log('✅ JSON parsé avec succès:', data);
            } catch (jsonError) {
                console.error('❌ Erreur parsing JSON:', jsonError);
                console.log('📄 Contenu qui a échoué:', responseText);
                data = { reply: responseText };
            }
        }

        console.log('Data reçue:', data);

        // Vérification de sécurité supplémentaire
        const rawContent = JSON.stringify(data);
        if (rawContent.includes('<iframe') || rawContent.includes('<script')) {
            console.log('🚨 Contenu dangereux détecté dans la réponse brute');
        }

        // Gestion automatique de la réponse
        let aiMsg = {
            id: (Date.now()+1).toString(),
            role: 'assistant',
            content: processResponseContent(data),
            timestamp: new Date(),
            attachment: null
        };

        // Détection du type de réponse
        if (data.attachment) {
            aiMsg.attachment = data.attachment;
            displayInRightPanel(data.attachment);
        } else if (data.image) {
            aiMsg.attachment = {
                type: 'image',
                data: data.image,
                fileName: 'image_from_webhook.png'
            };
            displayInRightPanel(aiMsg.attachment);
        } else if (data.html) {
            aiMsg.attachment = {
                type: 'html',
                data: data.html,
                fileName: 'html_from_webhook.html'
            };
            displayInRightPanel(aiMsg.attachment);
        } else if (isImageURL(aiMsg.content)) {
            aiMsg.attachment = {
                type: 'image',
                data: aiMsg.content,
                fileName: 'webhook_image'
            };
            displayInRightPanel(aiMsg.attachment);
        } else if (isHTML(aiMsg.content) && !aiMsg.content.includes('⚠️ Contenu HTML détecté')) {
            // Seulement si ce n'est pas déjà marqué comme dangereux
            aiMsg.attachment = {
                type: 'html',
                data: aiMsg.content,
                fileName: 'webhook_html'
            };
            displayInRightPanel(aiMsg.attachment);
            // Remplacer le contenu par un message plus sûr
            aiMsg.content = '📄 Contenu HTML reçu (voir panneau de droite)';
        }

        chat.messages.push(aiMsg);
        renderMessages();
    } catch (err) {
        console.error('Erreur complète:', err);
        let errorMessage = `⚠️ Erreur : ${err.message}`;

        // Messages d'erreur plus spécifiques
        if (err.message.includes('Failed to fetch')) {
            errorMessage = `⚠️ Erreur de connexion : Impossible de joindre le webhook n8n directement. Problème de réseau ou CORS.`;
        } else if (err.message.includes('CORS')) {
            errorMessage = `⚠️ Erreur CORS : Le serveur n8n doit être configuré pour accepter les requêtes cross-origin.`;
        } else if (err.message.includes('404')) {
            errorMessage = `⚠️ Erreur 404 : Le webhook n'existe pas à cette adresse.`;
        } else if (err.message.includes('500')) {
            errorMessage = `⚠️ Erreur serveur : Le webhook a rencontré une erreur interne.`;
        }

        chat.messages.push({
            id: Date.now().toString(),
            role: 'assistant',
            content: errorMessage,
            timestamp: new Date()
        });
        renderMessages();
    }
    hideTypingIndicator();
    saveChats();
}

/* ---------- Traitement des réponses ---------- */
function processResponseContent(data) {
    console.log('🔍 Traitement de la réponse:', data);
    console.log('🔍 Type de data:', typeof data);

    let content = '';

    // Gestion spéciale pour les différents formats de réponse n8n
    if (Array.isArray(data)) {
        console.log('📋 Réponse est un tableau');
        if (data.length > 0) {
            const firstItem = data[0];
            content = firstItem.output || firstItem.reply || firstItem.message || firstItem.text || JSON.stringify(firstItem, null, 2);
        }
    } else if (typeof data === 'object' && data !== null) {
        console.log('📦 Réponse est un objet');
        // Priorité : reply > output > message > response > content > myField > contenu brut
        content = data.reply || data.output || data.message || data.response || data.content || data.text || data.myField || '';

        // Si aucun champ standard trouvé, prendre le premier champ non-null
        if (!content) {
            const keys = Object.keys(data);
            console.log('� Clés disponibles:', keys);
            for (const key of keys) {
                if (data[key] && typeof data[key] === 'string') {
                    content = data[key];
                    console.log(`� Utilisation du champ "${key}":`, content);
                    break;
                }
            }
        }

        // Si toujours rien, afficher l'objet complet
        if (!content) {
            content = JSON.stringify(data, null, 2);
        }
    } else {
        console.log('📄 Réponse est primitive');
        content = String(data);
    }

    console.log('📝 Contenu extrait:', content);
    console.log('📏 Longueur du contenu:', content.length);

    // Si le contenu est vide, retourner un message par défaut
    if (!content || content.trim() === '' || content === 'undefined' || content === 'null') {
        console.log('⚠️ Contenu vide détecté');
        return 'Réponse reçue du webhook (contenu vide)';
    }

    // Vérifier si c'est du HTML dangereux AVANT tout traitement
    if (content.includes('<iframe') || content.includes('<script') || content.includes('<object') || content.includes('<embed')) {
        console.log('⚠️ HTML dangereux détecté, neutralisation');
        return `⚠️ Contenu HTML potentiellement dangereux détecté et neutralisé pour votre sécurité.`;
    }

    console.log('✅ Contenu final traité:', content);
    return content;
}

/* ---------- Détection automatique ---------- */
function isImageURL(str) {
    return str && str.match(/^https?:\/\/.+\.(jpg|jpeg|png|gif|svg|webp)(\?.*)?$/i);
}
function isHTML(str) {
    return str && (str.includes('<iframe') || str.includes('<html') || str.includes('<div') || (str.trim().startsWith('<') && str.trim().endsWith('>')));
}
function containsHTML(str) {
    return str && (str.includes('<') && str.includes('>'));
}

/* ---------- Affichage des messages ---------- */
function renderMessages() {
    const container = document.getElementById('messages');
    container.innerHTML = '';
    const chat = chats.find(c=>c.id===currentChatId);
    if(!chat) return;

    chat.messages.forEach(m=>{
        const div = document.createElement('div');
        div.className = 'message ' + m.role;
        const avatar = m.role === 'user' ? '👤' : '🤖';

        // Créer les éléments de manière sécurisée
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        avatarDiv.textContent = avatar;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        // Vérifier si le contenu contient du HTML dangereux
        const content = m.content || '';
        console.log('🎨 Rendu message:', { role: m.role, content: content, length: content.length });

        if (content.includes('<iframe') || content.includes('<script') || content.includes('<object')) {
            console.log('🚨 HTML dangereux détecté dans le rendu');
            // Afficher comme texte brut
            contentDiv.textContent = `⚠️ Contenu HTML neutralisé: ${content}`;
        } else {
            // Utiliser innerHTML seulement si c'est sûr
            const formattedContent = formatContent(content);
            console.log('✅ Contenu formaté:', formattedContent);
            contentDiv.innerHTML = formattedContent;
        }

        div.appendChild(avatarDiv);
        div.appendChild(contentDiv);
        container.appendChild(div);

        // Gérer les pièces jointes
        if(m.attachment){
            const att = document.createElement('div');
            att.style.marginTop = '8px';
            if(m.attachment.type === 'image'){
                att.innerHTML = `<img src="${m.attachment.data}" style="max-width:200px;border-radius:8px;cursor:pointer" onclick='displayInRightPanel(${JSON.stringify(m.attachment)})'>`;
            }else if(m.attachment.type === 'html'){
                att.innerHTML = `<div style="padding:8px;background:rgba(0,0,0,.2);border-radius:4px;font-size:12px;cursor:pointer" onclick='displayInRightPanel(${JSON.stringify(m.attachment)})'>📄 Code HTML</div>`;
            }
            contentDiv.appendChild(att);
        }
    });
    container.scrollTop = container.scrollHeight;
}
function formatContent(t){
    if (!t) return '';

    let content = String(t);

    // Vérifier si le contenu contient des éléments HTML dangereux
    if (content.includes('<iframe') || content.includes('<script') || content.includes('<object') || content.includes('<embed')) {
        // Afficher comme texte brut pour la sécurité
        return `<div style="background:rgba(239,68,68,.1);border:1px solid rgba(239,68,68,.3);padding:12px;border-radius:8px;color:#fca5a5;">
            <strong>⚠️ Contenu HTML détecté</strong><br>
            Pour votre sécurité, le contenu HTML a été neutralisé :<br><br>
            <code style="background:rgba(0,0,0,.3);padding:8px;border-radius:4px;display:block;white-space:pre-wrap;font-size:12px;">${content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code>
        </div>`;
    }

    // Échapper le HTML pour éviter l'injection
    content = content
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');

    // Puis appliquer le formatage markdown
    content = content
        .replace(/\n/g,'<br>')
        .replace(/(https?:\/\/[^\s]+)/g,'<a href="$1" target="_blank" style="color:var(--accent-color)">$1</a>')
        .replace(/```([\s\S]*?)```/g,'<pre style="background:rgba(0,0,0,.3);padding:12px;border-radius:8px;overflow-x:auto;margin:8px 0;">$1</pre>')
        .replace(/`([^`]+)`/g,'<code style="background:rgba(0,0,0,.3);padding:2px 6px;border-radius:4px;">$1</code>')
        .replace(/\*\*(.*?)\*\*/g,'<strong>$1</strong>')
        .replace(/\*(.*?)\*/g,'<em>$1</em>');

    return content;
}

/* ---------- Pièces jointes ---------- */
function handleFileUpload(e){
    const file=e.target.files[0];
    if(!file)return;
    const reader=new FileReader();
    reader.onload=ev=>{
        currentAttachment={type:file.type.startsWith('image/')?'image':'html',fileName:file.name,data:ev.target.result};
        document.getElementById('attachment-name').textContent=file.name;
        document.getElementById('attachment-preview').style.display='block';
    };
    if(file.type.startsWith('image/')) reader.readAsDataURL(file);
    else if(file.type==='text/html'||file.name.endsWith('.html')) reader.readAsText(file);
}
function removeAttachment(){
    currentAttachment=null;
    document.getElementById('attachment-preview').style.display='none';
    document.getElementById('file-input').value='';
}

/* ---------- Panel droit ---------- */
function displayInRightPanel(att){
    const pc=document.getElementById('panel-content');
    pc.innerHTML='';
    if(att.type==='image'){
        pc.innerHTML=`<div class="panel-section"><h3>Image</h3><img class="image-preview" src="${att.data}"><p style="font-size:14px;color:var(--text-secondary)">${att.fileName}</p></div>`;
    }else if(att.type==='html'){
        const safeHtml = escapeHtml(att.data);
        const isUnsafe = att.data.includes('<iframe') || att.data.includes('<script') || att.data.includes('<object');

        if (isUnsafe) {
            pc.innerHTML=`<div class="panel-section">
                <h3>⚠️ HTML (Mode sécurisé)</h3>
                <div class="html-preview">${safeHtml}</div>
                <p style="color:var(--error);font-size:12px;margin-top:8px;">Contenu HTML potentiellement dangereux affiché comme texte pour votre sécurité.</p>
            </div>`;
        } else {
            pc.innerHTML=`<div class="panel-section">
                <h3>HTML</h3>
                <div class="html-preview">${safeHtml}</div>
                <div style="border:1px solid var(--border-color);border-radius:8px;padding:12px;margin-top:12px;">${att.data}</div>
            </div>`;
        }
    }
}
function clearRightPanel(){ displayInRightPanel({type:'html',data:'',fileName:''}); }
function escapeHtml(t){const d=document.createElement('div');d.textContent=t;return d.innerHTML;}

/* ---------- Indicateur & panels ---------- */
function showTypingIndicator(){document.getElementById('typing-indicator').style.display='flex';}
function hideTypingIndicator(){document.getElementById('typing-indicator').style.display='none';}
function toggleSidebar(){document.getElementById('sidebar').classList.toggle('hidden');}
function toggleRightPanel(){document.getElementById('right-panel').classList.toggle('hidden-right');}

/* ---------- Reset application ---------- */
function resetApp() {
    if (confirm('⚠️ Cela va supprimer tout l\'historique des conversations. Continuer ?')) {
        localStorage.removeItem('ai-chat-chats');
        chats = [];
        newChat();
        clearRightPanel();
        console.log('🔄 Application réinitialisée');
    }
}

/* ---------- Test webhook ---------- */
async function testWebhook() {
    console.log('🧪 Test du webhook...');
    try {
        const response = await fetch('http://localhost:3001/webhook-proxy/analyse', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ message: 'Test de connexion' })
        });

        console.log('📡 Réponse brute:', response);
        const data = await response.json();
        console.log('📦 Données reçues:', data);

        alert(`Test réussi!\nStatus: ${response.status}\nDonnées: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
        console.error('❌ Erreur test:', error);
        alert(`Test échoué: ${error.message}`);
    }
}
</script>
</body>
</html>