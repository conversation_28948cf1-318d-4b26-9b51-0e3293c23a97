/**
 * Processeur de réponses - Traite et formate les réponses du webhook
 * Principe Single Responsibility: Responsable uniquement du traitement des réponses
 * Principe Open/Closed: Extensible pour nouveaux types de réponses
 */
class ResponseProcessor {
    constructor() {
        this.processors = new Map();
        this.initializeProcessors();
    }

    /**
     * Initialise les processeurs par type de contenu
     */
    initializeProcessors() {
        this.processors.set(CONTENT_TYPES.IMAGE, this.processImageResponse.bind(this));
        this.processors.set(CONTENT_TYPES.HTML, this.processHtmlResponse.bind(this));
        this.processors.set(CONTENT_TYPES.JSON, this.processJsonResponse.bind(this));
        this.processors.set(CONTENT_TYPES.TEXT, this.processTextResponse.bind(this));
    }

    /**
     * Traite une réponse du webhook
     * @param {Object} data - Les données de réponse
     * @returns {Object} - Réponse traitée
     */
    processResponse(data) {
        console.log('🔍 Traitement de la réponse:', data);
        console.log('🔍 Type de data:', typeof data);

        try {
            const extractedContent = this.extractContent(data);
            const contentType = this.detectContentType(extractedContent);
            
            console.log('📝 Contenu extrait:', extractedContent);
            console.log('🏷️ Type détecté:', contentType);

            const processor = this.processors.get(contentType) || this.processTextResponse.bind(this);
            return processor(extractedContent, data);

        } catch (error) {
            console.error('❌ Erreur traitement réponse:', error);
            return {
                content: 'Erreur lors du traitement de la réponse',
                type: CONTENT_TYPES.TEXT,
                hasError: true
            };
        }
    }

    /**
     * Extrait le contenu principal de la réponse
     * @param {Object} data - Les données brutes
     * @returns {string}
     */
    extractContent(data) {
        // Gestion spéciale pour les tableaux (format n8n)
        if (Array.isArray(data)) {
            console.log('📋 Réponse est un tableau');
            if (data.length > 0) {
                const firstItem = data[0];
                return firstItem.output || firstItem.reply || firstItem.message || 
                       firstItem.text || JSON.stringify(firstItem, null, 2);
            }
            return '';
        }

        // Gestion des objets
        if (typeof data === 'object' && data !== null) {
            console.log('📦 Réponse est un objet');
            
            // Priorité des champs
            const contentFields = ['reply', 'output', 'message', 'response', 'content', 'text'];
            
            for (const field of contentFields) {
                if (data[field]) {
                    return data[field];
                }
            }

            // Si aucun champ standard, chercher le premier champ string
            const keys = Object.keys(data);
            console.log('🔑 Clés disponibles:', keys);
            
            for (const key of keys) {
                if (data[key] && typeof data[key] === 'string') {
                    console.log(`📝 Utilisation du champ "${key}":`, data[key]);
                    return data[key];
                }
            }

            // Dernier recours: JSON complet
            return JSON.stringify(data, null, 2);
        }

        // Contenu primitif
        return String(data || '');
    }

    /**
     * Détecte le type de contenu
     * @param {string} content - Le contenu à analyser
     * @returns {string}
     */
    detectContentType(content) {
        if (!content || typeof content !== 'string') {
            return CONTENT_TYPES.TEXT;
        }

        // Détection d'image PNG corrompue
        if (content.startsWith('�PNG')) {
            return CONTENT_TYPES.IMAGE;
        }

        // Détection HTML
        if (this.isHtmlContent(content)) {
            return CONTENT_TYPES.HTML;
        }

        // Détection JSON
        if (this.isJsonContent(content)) {
            return CONTENT_TYPES.JSON;
        }

        return CONTENT_TYPES.TEXT;
    }

    /**
     * Vérifie si le contenu est HTML
     * @param {string} content - Le contenu à vérifier
     * @returns {boolean}
     */
    isHtmlContent(content) {
        return content.includes('<iframe') || 
               content.includes('<html') || 
               content.includes('<div') || 
               (content.trim().startsWith('<') && content.trim().endsWith('>'));
    }

    /**
     * Vérifie si le contenu est JSON
     * @param {string} content - Le contenu à vérifier
     * @returns {boolean}
     */
    isJsonContent(content) {
        return (content.startsWith('{') || content.startsWith('[')) && 
               content.length > 2;
    }

    /**
     * Traite une réponse image
     * @param {string} content - Le contenu
     * @param {Object} originalData - Les données originales
     * @returns {Object}
     */
    processImageResponse(content, originalData) {
        console.log('🖼️ Traitement réponse image');
        
        if (content.startsWith('�PNG')) {
            return {
                content: CONFIG.DEFAULT_MESSAGES.IMAGE_CORRUPTED,
                type: CONTENT_TYPES.IMAGE,
                hasError: true
            };
        }

        return {
            content: originalData.reply || CONFIG.DEFAULT_MESSAGES.CHART_GENERATED,
            type: CONTENT_TYPES.IMAGE,
            attachment: {
                type: CONTENT_TYPES.IMAGE,
                data: originalData.image,
                fileName: 'chart_from_webhook.png'
            }
        };
    }

    /**
     * Traite une réponse HTML
     * @param {string} content - Le contenu HTML
     * @returns {Object}
     */
    processHtmlResponse(content) {
        console.log('🌐 Traitement réponse HTML');
        
        const isDangerous = this.isDangerousHtml(content);
        
        if (isDangerous) {
            return {
                content: CONFIG.DEFAULT_MESSAGES.HTML_DANGEROUS,
                type: CONTENT_TYPES.HTML,
                hasError: true
            };
        }

        return {
            content: CONFIG.DEFAULT_MESSAGES.HTML_CONTENT,
            type: CONTENT_TYPES.HTML,
            attachment: {
                type: CONTENT_TYPES.HTML,
                data: content,
                fileName: 'webhook_html.html'
            }
        };
    }

    /**
     * Traite une réponse JSON
     * @param {string} content - Le contenu JSON
     * @returns {Object}
     */
    processJsonResponse(content) {
        console.log('📋 Traitement réponse JSON');
        
        try {
            const parsed = JSON.parse(content);
            const formatted = JSON.stringify(parsed, null, 2);
            
            return {
                content: formatted,
                type: CONTENT_TYPES.JSON
            };
        } catch (error) {
            return {
                content: content,
                type: CONTENT_TYPES.TEXT
            };
        }
    }

    /**
     * Traite une réponse texte
     * @param {string} content - Le contenu texte
     * @returns {Object}
     */
    processTextResponse(content) {
        console.log('📝 Traitement réponse texte');
        
        if (!content || content.trim() === '' || content === 'undefined' || content === 'null') {
            return {
                content: CONFIG.DEFAULT_MESSAGES.EMPTY_RESPONSE,
                type: CONTENT_TYPES.TEXT
            };
        }

        return {
            content: content,
            type: CONTENT_TYPES.TEXT
        };
    }

    /**
     * Vérifie si le HTML est dangereux
     * @param {string} html - Le HTML à vérifier
     * @returns {boolean}
     */
    isDangerousHtml(html) {
        const dangerousTags = ['<iframe', '<script', '<object', '<embed'];
        return dangerousTags.some(tag => html.includes(tag));
    }

    /**
     * Ajoute un nouveau processeur
     * @param {string} type - Le type de contenu
     * @param {Function} processor - La fonction de traitement
     */
    addProcessor(type, processor) {
        if (typeof processor !== 'function') {
            throw new Error('Le processeur doit être une fonction');
        }
        this.processors.set(type, processor);
    }

    /**
     * Supprime un processeur
     * @param {string} type - Le type de contenu
     */
    removeProcessor(type) {
        this.processors.delete(type);
    }
}
