/**
 * Contrôleur Modal - Gère les modals et le panneau droit
 * Principe Single Responsibility: Responsable uniquement des modals
 */
class ModalController {
    constructor(appController) {
        this.app = appController;
        this.isFullscreenOpen = false;
    }

    /**
     * Affiche du contenu dans le panneau droit
     * @param {Object} attachment - La pièce jointe à afficher
     */
    displayInRightPanel(attachment) {
        const panelContent = document.getElementById('panel-content');
        if (!panelContent) {
            console.error('❌ Panel content non trouvé');
            return;
        }

        panelContent.innerHTML = '';

        if (attachment.type === CONTENT_TYPES.IMAGE) {
            this.displayImageInPanel(attachment, panelContent);
        } else if (attachment.type === CONTENT_TYPES.HTML) {
            this.displayHtmlInPanel(attachment, panelContent);
        } else {
            this.displayGenericInPanel(attachment, panelContent);
        }

        console.log('📱 Contenu affiché dans le panneau droit:', attachment.type);
    }

    /**
     * Affiche une image dans le panneau
     * @param {Object} attachment - L'image
     * @param {HTMLElement} container - Le conteneur
     */
    displayImageInPanel(attachment, container) {
        const imageId = 'preview-image-' + Date.now();
        
        const section = document.createElement('div');
        section.className = 'panel-section';
        
        section.innerHTML = `
            <div class="panel-header">
                <h3>📊 Graphique</h3>
                <div class="panel-actions">
                    <button onclick="modalController.openFullscreen('${imageId}')" 
                            class="btn btn-primary btn-sm" 
                            title="Plein écran">
                        🔍 Agrandir
                    </button>
                    <button onclick="modalController.downloadImage('${attachment.data}', '${attachment.fileName}')" 
                            class="btn btn-success btn-sm" 
                            title="Télécharger">
                        💾 Télécharger
                    </button>
                </div>
            </div>
            <img id="${imageId}" 
                 class="image-preview" 
                 src="${attachment.data}" 
                 onclick="modalController.openFullscreen('${imageId}')"
                 title="Cliquer pour agrandir">
            <p class="image-info">${attachment.fileName}</p>
        `;
        
        container.appendChild(section);
    }

    /**
     * Affiche du HTML dans le panneau
     * @param {Object} attachment - Le HTML
     * @param {HTMLElement} container - Le conteneur
     */
    displayHtmlInPanel(attachment, container) {
        const section = document.createElement('div');
        section.className = 'panel-section';
        
        const isUnsafe = this.isDangerousHtml(attachment.data);
        
        if (isUnsafe) {
            section.innerHTML = `
                <div class="panel-header">
                    <h3>⚠️ HTML (Mode sécurisé)</h3>
                </div>
                <div class="html-preview safe-mode">${this.escapeHtml(attachment.data)}</div>
                <p class="warning-text">Contenu HTML potentiellement dangereux affiché comme texte pour votre sécurité.</p>
            `;
        } else {
            section.innerHTML = `
                <div class="panel-header">
                    <h3>🌐 HTML</h3>
                    <div class="panel-actions">
                        <button onclick="modalController.downloadHtml('${attachment.data.replace(/'/g, "\\'")}', '${attachment.fileName}')" 
                                class="btn btn-success btn-sm" 
                                title="Télécharger">
                            💾 Télécharger
                        </button>
                    </div>
                </div>
                <div class="html-preview">${this.escapeHtml(attachment.data)}</div>
                <div class="html-render">${attachment.data}</div>
            `;
        }
        
        container.appendChild(section);
    }

    /**
     * Affiche un contenu générique dans le panneau
     * @param {Object} attachment - Le contenu
     * @param {HTMLElement} container - Le conteneur
     */
    displayGenericInPanel(attachment, container) {
        const section = document.createElement('div');
        section.className = 'panel-section';
        
        section.innerHTML = `
            <div class="panel-header">
                <h3>📄 Contenu</h3>
            </div>
            <div class="generic-content">
                <p><strong>Type:</strong> ${attachment.type}</p>
                <p><strong>Fichier:</strong> ${attachment.fileName}</p>
                <div class="content-preview">${this.escapeHtml(String(attachment.data).substring(0, 500))}...</div>
            </div>
        `;
        
        container.appendChild(section);
    }

    /**
     * Ouvre une image en plein écran
     * @param {string} imageId - L'ID de l'image
     */
    openFullscreen(imageId) {
        const sourceImg = document.getElementById(imageId);
        const modal = document.getElementById('fullscreen-modal');
        const fullscreenImg = document.getElementById('fullscreen-image');
        const downloadBtn = document.getElementById('fullscreen-download');

        if (!sourceImg || !modal || !fullscreenImg) {
            console.error('❌ Éléments fullscreen non trouvés');
            return;
        }

        fullscreenImg.src = sourceImg.src;
        modal.style.display = 'flex';
        this.isFullscreenOpen = true;

        // Configurer le bouton de téléchargement
        const fileName = this.extractFileName(sourceImg) || 'graphique.png';
        downloadBtn.onclick = () => this.downloadImage(sourceImg.src, fileName);

        // Émettre un événement
        document.dispatchEvent(new CustomEvent(EVENTS.FULLSCREEN_OPENED, {
            detail: { imageId, src: sourceImg.src }
        }));

        console.log('🔍 Plein écran ouvert:', imageId);
    }

    /**
     * Ferme le plein écran
     * @param {Event} event - L'événement de clic (optionnel)
     */
    closeFullscreen(event) {
        // Si on clique sur l'image elle-même, ne pas fermer
        if (event && event.target.id === 'fullscreen-image') {
            return;
        }

        const modal = document.getElementById('fullscreen-modal');
        if (modal) {
            modal.style.display = 'none';
            this.isFullscreenOpen = false;

            // Émettre un événement
            document.dispatchEvent(new CustomEvent(EVENTS.FULLSCREEN_CLOSED));

            console.log('❌ Plein écran fermé');
        }
    }

    /**
     * Télécharge une image
     * @param {string} dataUrl - L'URL de données de l'image
     * @param {string} fileName - Le nom du fichier
     */
    downloadImage(dataUrl, fileName) {
        try {
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = fileName || 'graphique.png';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('📥 Téléchargement initié:', fileName);
            
            if (this.app.uiController) {
                this.app.uiController.showNotification('Image téléchargée avec succès', 'success');
            }

        } catch (error) {
            console.error('❌ Erreur téléchargement:', error);
            
            if (this.app.uiController) {
                this.app.uiController.showNotification(CONFIG.ERROR_MESSAGES.DOWNLOAD_ERROR, 'error');
            }
        }
    }

    /**
     * Télécharge du HTML
     * @param {string} htmlContent - Le contenu HTML
     * @param {string} fileName - Le nom du fichier
     */
    downloadHtml(htmlContent, fileName) {
        try {
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName || 'contenu.html';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(url);

            console.log('📥 HTML téléchargé:', fileName);
            
            if (this.app.uiController) {
                this.app.uiController.showNotification('Fichier HTML téléchargé avec succès', 'success');
            }

        } catch (error) {
            console.error('❌ Erreur téléchargement HTML:', error);
            
            if (this.app.uiController) {
                this.app.uiController.showNotification('Erreur lors du téléchargement', 'error');
            }
        }
    }

    /**
     * Vide le panneau droit
     */
    clearRightPanel() {
        const panelContent = document.getElementById('panel-content');
        if (panelContent) {
            panelContent.innerHTML = `
                <div class="panel-section">
                    <h3>Contenu réponse</h3>
                    <p class="text-secondary">Les contenus retournés par le webhook seront affichés ici.</p>
                </div>
            `;
        }
    }

    /**
     * Vérifie si le HTML est dangereux
     * @param {string} html - Le HTML à vérifier
     * @returns {boolean}
     */
    isDangerousHtml(html) {
        const dangerousTags = ['<iframe', '<script', '<object', '<embed'];
        return dangerousTags.some(tag => html.includes(tag));
    }

    /**
     * Échappe le HTML
     * @param {string} text - Le texte à échapper
     * @returns {string}
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Extrait le nom de fichier d'une image
     * @param {HTMLImageElement} img - L'élément image
     * @returns {string}
     */
    extractFileName(img) {
        // Chercher dans les éléments parents pour le nom de fichier
        const section = img.closest('.panel-section');
        if (section) {
            const info = section.querySelector('.image-info, p');
            if (info) {
                return info.textContent.trim();
            }
        }
        
        return 'graphique.png';
    }

    /**
     * Vérifie si le plein écran est ouvert
     * @returns {boolean}
     */
    isFullscreenActive() {
        return this.isFullscreenOpen;
    }
}
