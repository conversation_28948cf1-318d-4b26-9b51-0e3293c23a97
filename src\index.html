<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assistant IA - <PERSON><PERSON><PERSON></title>
    
    <!-- Styles -->
    <link rel="stylesheet" href="./styles/main.css">
    <link rel="stylesheet" href="./styles/components.css">
    <link rel="stylesheet" href="./styles/modal.css">
</head>
<body>
    <!-- Sidebar gauche -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2>Historique</h2>
            <div class="sidebar-actions">
                <button class="btn btn-primary btn-sm" onclick="chatController.newChat()">+ Nouveau</button>
                <button class="btn btn-danger btn-sm" onclick="appController.resetApp()" title="Reset complet">🔄</button>
                <button class="btn btn-success btn-sm" onclick="webhookService.testWebhook()" title="Test webhook">🧪</button>
            </div>
        </div>
        <div class="history-list" id="history-list"></div>
        <div class="toggle-sidebar" onclick="uiController.toggleSidebar()">
            <svg width="16" height="16" fill="none" stroke="#fff" stroke-width="2">
                <polyline points="15 18 9 12 15 6"/>
            </svg>
        </div>
    </div>

    <!-- Zone centrale -->
    <div class="main">
        <div class="chat-container">
            <div class="chat-header">
                <h1>✨ Assistant IA</h1>
                <p>🚀 Analyse de données intelligente avec n8n</p>
            </div>
            <div class="messages" id="messages">
                <!-- Messages seront injectés ici -->
            </div>
            <div class="typing-indicator" id="typing-indicator">
                <div class="message-avatar">🤖</div>
                <div class="typing-dots">
                    <span></span><span></span><span></span>
                </div>
            </div>
            <div class="input-container">
                <div class="attachment-preview" id="attachment-preview">
                    <span class="file-name" id="attachment-name"></span>
                    <span class="remove-attachment" onclick="attachmentController.removeAttachment()">×</span>
                </div>
                <div class="input-wrapper">
                    <textarea id="message-input" class="message-input" placeholder="Posez votre question ou décrivez vos données à analyser…" rows="1"></textarea>
                    <input type="file" id="file-input" class="file-input" accept="image/*,.html" onchange="attachmentController.handleFileUpload(event)">
                    <button class="attachment-button" onclick="document.getElementById('file-input').click()" title="Joindre un fichier">
                        📎
                    </button>
                    <button class="send-button" id="send-button" onclick="messageController.sendMessage()" title="Envoyer le message">
                        <svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <line x1="22" y1="2" x2="11" y2="13"/>
                            <polygon points="22,2 15,22 11,13 2,9"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Panel droit -->
    <div class="right-panel" id="right-panel">
        <div class="right-panel-header">
            <h2>Visualisation</h2>
        </div>
        <div class="panel-content" id="panel-content">
            <div class="panel-section">
                <h3>Contenu réponse</h3>
                <p class="text-secondary">Les contenus retournés par le webhook seront affichés ici.</p>
            </div>
        </div>
        <div class="toggle-right-panel" onclick="uiController.toggleRightPanel()">
            <svg width="16" height="16" fill="none" stroke="#fff" stroke-width="2">
                <polyline points="9 18 15 12 9 6"/>
            </svg>
        </div>
    </div>

    <!-- Modal plein écran -->
    <div id="fullscreen-modal" class="fullscreen-modal" onclick="modalController.closeFullscreen(event)">
        <div class="fullscreen-content">
            <button class="fullscreen-close" onclick="modalController.closeFullscreen()">✕ Fermer</button>
            <button id="fullscreen-download" class="fullscreen-download">💾 Télécharger</button>
            <img id="fullscreen-image" class="fullscreen-image" src="" alt="Image en plein écran">
        </div>
    </div>

    <!-- Scripts -->
    <script src="./js/config/constants.js"></script>
    <script src="./js/models/Chat.js"></script>
    <script src="./js/models/Message.js"></script>
    <script src="./js/services/WebhookService.js"></script>
    <script src="./js/services/StorageService.js"></script>
    <script src="./js/services/ResponseProcessor.js"></script>
    <script src="./js/controllers/AppController.js"></script>
    <script src="./js/controllers/ChatController.js"></script>
    <script src="./js/controllers/MessageController.js"></script>
    <script src="./js/controllers/AttachmentController.js"></script>
    <script src="./js/controllers/UIController.js"></script>
    <script src="./js/controllers/ModalController.js"></script>
    <script src="./js/utils/ContentFormatter.js"></script>
    <script src="./js/utils/ImageUtils.js"></script>
    <script src="./js/app.js"></script>
</body>
</html>
