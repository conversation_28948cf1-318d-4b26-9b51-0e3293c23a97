/**
 * Contrôleur de chat - Gère les opérations sur les chats
 * Principe Single Responsibility: Responsable uniquement de la gestion des chats
 */
class ChatController {
    constructor(appController) {
        this.app = appController;
    }

    /**
     * Crée un nouveau chat
     * @returns {Chat}
     */
    createNewChat() {
        const chat = new Chat();
        this.app.addChat(chat);
        this.loadChat(chat.id);
        
        // Émettre un événement
        document.dispatchEvent(new CustomEvent(EVENTS.CHAT_CREATED, {
            detail: { chatId: chat.id }
        }));
        
        console.log('📝 Nouveau chat créé:', chat.id);
        return chat;
    }

    /**
     * Charge un chat spécifique
     * @param {string} chatId - L'ID du chat à charger
     */
    loadChat(chatId) {
        const chat = this.app.chats.find(c => c.id === chatId);
        if (!chat) {
            console.error('❌ Chat non trouvé:', chatId);
            return;
        }

        // Mettre à jour le chat actuel
        this.app.setCurrentChat(chatId);

        // Rendre les messages seulement si le messageController existe
        if (this.app.messageController && this.app.messageController.renderMessages) {
            this.app.messageController.renderMessages();
        }

        // Mettre à jour l'interface
        this.updateChatSelection(chatId);

        // Émettre un événement
        document.dispatchEvent(new CustomEvent(EVENTS.CHAT_LOADED, {
            detail: { chatId, messageCount: chat.messages.length }
        }));

        console.log('📂 Chat chargé:', chatId, `(${chat.messages.length} messages)`);
    }

    /**
     * Supprime un chat
     * @param {string} chatId - L'ID du chat à supprimer
     */
    deleteChat(chatId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette conversation ?')) {
            this.app.removeChat(chatId);
            this.renderChats();
            console.log('🗑️ Chat supprimé:', chatId);
        }
    }

    /**
     * Rend la liste des chats dans la sidebar
     */
    renderChats() {
        const container = document.getElementById('history-list');
        if (!container) {
            console.error('❌ Conteneur history-list non trouvé');
            return;
        }

        container.innerHTML = '';

        if (this.app.chats.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <p>Aucune conversation</p>
                    <button class="btn btn-primary btn-sm" onclick="chatController.createNewChat()">
                        Commencer une conversation
                    </button>
                </div>
            `;
            return;
        }

        // Trier les chats par timestamp (plus récent en premier)
        const sortedChats = [...this.app.chats].sort((a, b) => 
            new Date(b.timestamp) - new Date(a.timestamp)
        );

        sortedChats.forEach(chat => {
            const chatElement = this.createChatElement(chat);
            container.appendChild(chatElement);
        });
    }

    /**
     * Crée un élément DOM pour un chat
     * @param {Chat} chat - Le chat
     * @returns {HTMLElement}
     */
    createChatElement(chat) {
        const div = document.createElement('div');
        div.className = `chat-item ${chat.id === this.app.currentChatId ? 'active' : ''}`;
        div.dataset.chatId = chat.id;

        const lastMessage = chat.getLastMessage();
        const preview = lastMessage ? lastMessage.getContentPreview(50) : 'Conversation vide';
        const messageCount = chat.getMessageCount();
        const timestamp = this.formatTimestamp(chat.timestamp);

        div.innerHTML = `
            <div class="chat-header">
                <h4 class="chat-title" title="${chat.title}">${chat.title}</h4>
                <div class="chat-actions">
                    <button class="btn-icon" onclick="chatController.deleteChat('${chat.id}')" title="Supprimer">
                        🗑️
                    </button>
                </div>
            </div>
            <div class="chat-preview">${preview}</div>
            <div class="chat-meta">
                <span class="message-count">${messageCount} message${messageCount > 1 ? 's' : ''}</span>
                <span class="chat-timestamp">${timestamp}</span>
            </div>
        `;

        // Ajouter l'événement de clic
        div.addEventListener('click', (e) => {
            // Ne pas déclencher si on clique sur le bouton de suppression
            if (!e.target.closest('.chat-actions')) {
                this.loadChat(chat.id);
            }
        });

        return div;
    }

    /**
     * Met à jour la sélection visuelle du chat
     * @param {string} chatId - L'ID du chat sélectionné
     */
    updateChatSelection(chatId) {
        // Retirer la classe active de tous les chats
        document.querySelectorAll('.chat-item').forEach(item => {
            item.classList.remove('active');
        });

        // Ajouter la classe active au chat sélectionné
        const selectedChat = document.querySelector(`[data-chat-id="${chatId}"]`);
        if (selectedChat) {
            selectedChat.classList.add('active');
        }
    }

    /**
     * Formate un timestamp pour l'affichage
     * @param {Date} timestamp - Le timestamp à formater
     * @returns {string}
     */
    formatTimestamp(timestamp) {
        if (!(timestamp instanceof Date)) {
            timestamp = new Date(timestamp);
        }

        const now = new Date();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) {
            return 'À l\'instant';
        } else if (minutes < 60) {
            return `Il y a ${minutes} min`;
        } else if (hours < 24) {
            return `Il y a ${hours}h`;
        } else if (days < 7) {
            return `Il y a ${days} jour${days > 1 ? 's' : ''}`;
        } else {
            return timestamp.toLocaleDateString('fr-FR', {
                day: 'numeric',
                month: 'short'
            });
        }
    }

    /**
     * Recherche dans les chats
     * @param {string} query - La requête de recherche
     * @returns {Chat[]}
     */
    searchChats(query) {
        if (!query || query.trim() === '') {
            return this.app.chats;
        }

        const searchTerm = query.toLowerCase().trim();
        
        return this.app.chats.filter(chat => {
            // Recherche dans le titre
            if (chat.title.toLowerCase().includes(searchTerm)) {
                return true;
            }

            // Recherche dans les messages
            return chat.messages.some(message => 
                message.content.toLowerCase().includes(searchTerm)
            );
        });
    }

    /**
     * Filtre les chats par critères
     * @param {Object} criteria - Les critères de filtrage
     * @returns {Chat[]}
     */
    filterChats(criteria = {}) {
        let filteredChats = [...this.app.chats];

        // Filtrer par date
        if (criteria.dateFrom) {
            const fromDate = new Date(criteria.dateFrom);
            filteredChats = filteredChats.filter(chat => 
                chat.timestamp >= fromDate
            );
        }

        if (criteria.dateTo) {
            const toDate = new Date(criteria.dateTo);
            filteredChats = filteredChats.filter(chat => 
                chat.timestamp <= toDate
            );
        }

        // Filtrer par nombre de messages
        if (criteria.minMessages) {
            filteredChats = filteredChats.filter(chat => 
                chat.getMessageCount() >= criteria.minMessages
            );
        }

        // Filtrer par présence de pièces jointes
        if (criteria.hasAttachments) {
            filteredChats = filteredChats.filter(chat => 
                chat.messages.some(message => message.hasAttachment())
            );
        }

        return filteredChats;
    }

    /**
     * Exporte un chat au format JSON
     * @param {string} chatId - L'ID du chat à exporter
     * @returns {string}
     */
    exportChat(chatId) {
        const chat = this.app.chats.find(c => c.id === chatId);
        if (!chat) {
            throw new Error('Chat non trouvé');
        }

        const exportData = {
            title: chat.title,
            timestamp: chat.timestamp,
            messages: chat.messages.map(msg => ({
                role: msg.role,
                content: msg.content,
                timestamp: msg.timestamp,
                hasAttachment: msg.hasAttachment()
            }))
        };

        return JSON.stringify(exportData, null, 2);
    }

    /**
     * Duplique un chat
     * @param {string} chatId - L'ID du chat à dupliquer
     * @returns {Chat}
     */
    duplicateChat(chatId) {
        const originalChat = this.app.chats.find(c => c.id === chatId);
        if (!originalChat) {
            throw new Error('Chat non trouvé');
        }

        const newChat = new Chat(null, originalChat.title + ' (copie)');
        
        // Copier les messages
        originalChat.messages.forEach(msg => {
            const newMessage = new Message(msg.content, msg.role, msg.attachment);
            newChat.addMessage(newMessage);
        });

        this.app.addChat(newChat);
        this.renderChats();
        
        return newChat;
    }

    /**
     * Obtient les statistiques des chats
     * @returns {Object}
     */
    getChatStatistics() {
        const totalChats = this.app.chats.length;
        const totalMessages = this.app.chats.reduce((sum, chat) => 
            sum + chat.getMessageCount(), 0
        );
        
        const chatsWithAttachments = this.app.chats.filter(chat =>
            chat.messages.some(msg => msg.hasAttachment())
        ).length;

        const averageMessagesPerChat = totalChats > 0 ? 
            Math.round(totalMessages / totalChats) : 0;

        return {
            totalChats,
            totalMessages,
            chatsWithAttachments,
            averageMessagesPerChat
        };
    }
}
