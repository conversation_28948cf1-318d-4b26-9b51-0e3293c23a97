/**
 * Modèle Message - Représente un message dans une conversation
 * Principe Single Responsibility: Gère uniquement les données d'un message
 */
class Message {
    constructor(content, role = MESSAGE_ROLES.USER, attachment = null) {
        this.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        this.content = content || '';
        this.role = this.validateRole(role);
        this.timestamp = new Date();
        this.attachment = attachment;
        this.isProcessed = false;
    }

    /**
     * Valide le rôle du message
     * @param {string} role - Le rôle à valider
     * @returns {string}
     */
    validateRole(role) {
        const validRoles = Object.values(MESSAGE_ROLES);
        if (!validRoles.includes(role)) {
            console.warn(`Rôle invalide: ${role}. Utilisation de ${MESSAGE_ROLES.USER} par défaut.`);
            return MESSAGE_ROLES.USER;
        }
        return role;
    }

    /**
     * Définit le contenu du message
     * @param {string} content - Le nouveau contenu
     */
    setContent(content) {
        if (typeof content !== 'string') {
            throw new Error('Le contenu doit être une chaîne de caractères');
        }
        this.content = content;
        this.updateTimestamp();
    }

    /**
     * Ajoute ou met à jour une pièce jointe
     * @param {Object} attachment - La pièce jointe
     */
    setAttachment(attachment) {
        if (attachment && !this.isValidAttachment(attachment)) {
            throw new Error('Pièce jointe invalide');
        }
        this.attachment = attachment;
        this.updateTimestamp();
    }

    /**
     * Valide une pièce jointe
     * @param {Object} attachment - La pièce jointe à valider
     * @returns {boolean}
     */
    isValidAttachment(attachment) {
        return attachment &&
               typeof attachment === 'object' &&
               attachment.type &&
               attachment.data &&
               typeof attachment.fileName === 'string';
    }

    /**
     * Vérifie si le message a une pièce jointe
     * @returns {boolean}
     */
    hasAttachment() {
        return this.attachment !== null && this.attachment !== undefined;
    }

    /**
     * Vérifie si le message a une pièce jointe d'un type spécifique
     * @param {string} type - Le type à vérifier
     * @returns {boolean}
     */
    hasAttachmentOfType(type) {
        return this.hasAttachment() && this.attachment.type === type;
    }

    /**
     * Retourne l'avatar approprié pour le rôle
     * @returns {string}
     */
    getAvatar() {
        return this.role === MESSAGE_ROLES.USER ? '👤' : '🤖';
    }

    /**
     * Vérifie si le message est de l'utilisateur
     * @returns {boolean}
     */
    isUserMessage() {
        return this.role === MESSAGE_ROLES.USER;
    }

    /**
     * Vérifie si le message est de l'assistant
     * @returns {boolean}
     */
    isAssistantMessage() {
        return this.role === MESSAGE_ROLES.ASSISTANT;
    }

    /**
     * Met à jour le timestamp
     */
    updateTimestamp() {
        this.timestamp = new Date();
    }

    /**
     * Marque le message comme traité
     */
    markAsProcessed() {
        this.isProcessed = true;
    }

    /**
     * Vérifie si le contenu est vide
     * @returns {boolean}
     */
    isEmpty() {
        return !this.content || this.content.trim() === '';
    }

    /**
     * Retourne la longueur du contenu
     * @returns {number}
     */
    getContentLength() {
        return this.content ? this.content.length : 0;
    }

    /**
     * Retourne un aperçu du contenu (tronqué)
     * @param {number} maxLength - Longueur maximale
     * @returns {string}
     */
    getContentPreview(maxLength = 100) {
        if (!this.content) return '';
        return this.content.length > maxLength 
            ? this.content.substring(0, maxLength) + '...'
            : this.content;
    }

    /**
     * Sérialise le message pour le stockage
     * @returns {Object}
     */
    toJSON() {
        return {
            id: this.id,
            content: this.content,
            role: this.role,
            timestamp: this.timestamp.toISOString(),
            attachment: this.attachment,
            isProcessed: this.isProcessed
        };
    }

    /**
     * Crée un message à partir de données sérialisées
     * @param {Object} data - Les données sérialisées
     * @returns {Message}
     */
    static fromJSON(data) {
        const message = new Message(data.content, data.role, data.attachment);
        message.id = data.id;
        message.timestamp = new Date(data.timestamp);
        message.isProcessed = data.isProcessed || false;
        return message;
    }

    /**
     * Valide les données du message
     * @returns {boolean}
     */
    isValid() {
        return this.id &&
               typeof this.content === 'string' &&
               Object.values(MESSAGE_ROLES).includes(this.role) &&
               this.timestamp instanceof Date;
    }
}
