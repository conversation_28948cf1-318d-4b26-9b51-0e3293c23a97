const express = require('express');
const path = require('path');

const app = express();
const PORT = 3000;

// Servir les fichiers statiques depuis le dossier src
app.use(express.static(path.join(__dirname, 'src')));

// Servir aussi les fichiers depuis la racine pour compatibilité
app.use('/legacy', express.static(__dirname));

// Route pour la nouvelle version
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'src', 'index.html'));
});

// Route pour l'ancienne version (compatibilité)
app.get('/legacy', (req, res) => {
    res.sendFile(path.join(__dirname, 'AssistantIA.html'));
});

// Route de santé
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        version: '2.0.0',
        timestamp: new Date().toISOString()
    });
});

app.listen(PORT, () => {
    console.log(`🌐 Web server running on http://localhost:${PORT}`);
    console.log(`📱 Nouvelle version: http://localhost:${PORT}`);
    console.log(`🔄 Ancienne version: http://localhost:${PORT}/legacy`);
    console.log('Ouvrez votre navigateur sur ces adresses');
});
