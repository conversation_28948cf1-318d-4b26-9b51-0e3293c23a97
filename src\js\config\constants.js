/**
 * Configuration et constantes de l'application
 */
const CONFIG = {
    // URLs
    WEBHOOK_URL: 'http://localhost:3001/webhook-proxy/ChertMSX',
    
    // Storage
    STORAGE_KEY: 'ai-chat-chats',
    
    // UI
    MAX_MESSAGE_HEIGHT: 120,
    TYPING_ANIMATION_DURATION: 1400,
    
    // Files
    ACCEPTED_FILE_TYPES: 'image/*,.html',
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    
    // Messages
    DEFAULT_MESSAGES: {
        EMPTY_RESPONSE: 'Réponse reçue du webhook (contenu vide)',
        WEBHOOK_SUCCESS: 'Webhook exécuté avec succès',
        IMAGE_CORRUPTED: '🖼️ Image reçue mais corrompue lors de la transmission JSON. Le webhook doit retourner l\'image en base64 ou URL.',
        HTML_DANGEROUS: '⚠️ Contenu HTML potentiellement dangereux détecté et neutralisé pour votre sécurité.',
        CHART_GENERATED: '📊 Graphique généré avec succès (voir panneau de droite)',
        HTML_CONTENT: '📄 Contenu HTML reçu (voir panneau de droite)'
    },
    
    // Errors
    ERROR_MESSAGES: {
        FAILED_TO_FETCH: '⚠️ Erreur de connexion : Impossible de joindre le webhook n8n directement. Problème de réseau ou CORS.',
        CORS_ERROR: '⚠️ Erreur CORS : Le serveur n8n doit être configuré pour accepter les requêtes cross-origin.',
        NOT_FOUND: '⚠️ Erreur 404 : Le webhook n\'existe pas à cette adresse.',
        SERVER_ERROR: '⚠️ Erreur serveur : Le webhook a rencontré une erreur interne.',
        DOWNLOAD_ERROR: 'Erreur lors du téléchargement de l\'image'
    }
};

// Types de contenu
const CONTENT_TYPES = {
    IMAGE: 'image',
    HTML: 'html',
    TEXT: 'text',
    JSON: 'json'
};

// Rôles des messages
const MESSAGE_ROLES = {
    USER: 'user',
    ASSISTANT: 'assistant'
};

// États de l'application
const APP_STATES = {
    IDLE: 'idle',
    SENDING: 'sending',
    RECEIVING: 'receiving',
    ERROR: 'error'
};

// Événements personnalisés
const EVENTS = {
    CHAT_CREATED: 'chat:created',
    CHAT_LOADED: 'chat:loaded',
    MESSAGE_SENT: 'message:sent',
    MESSAGE_RECEIVED: 'message:received',
    ATTACHMENT_ADDED: 'attachment:added',
    ATTACHMENT_REMOVED: 'attachment:removed',
    FULLSCREEN_OPENED: 'fullscreen:opened',
    FULLSCREEN_CLOSED: 'fullscreen:closed'
};

// Export pour les modules (si nécessaire)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, CONTENT_TYPES, MESSAGE_ROLES, APP_STATES, EVENTS };
}
