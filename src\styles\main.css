/**
 * Styles principaux de l'application - Design moderne et élégant
 * Architecture CSS modulaire et maintenable
 */

/* Variables CSS pour la cohérence */
:root {
    /* Couleurs principales */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-quaternary: #0f3460;
    --bg-glass: rgba(255, 255, 255, 0.05);
    --bg-glass-hover: rgba(255, 255, 255, 0.1);

    /* Texte */
    --text-primary: #ffffff;
    --text-secondary: #b8bcc8;
    --text-muted: #6b7280;
    --text-accent: #e0e7ff;

    /* Couleurs d'accent */
    --accent-color: #6366f1;
    --accent-hover: #5855eb;
    --accent-light: #818cf8;
    --accent-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* Couleurs de statut */
    --success-color: #10b981;
    --success-hover: #059669;
    --success-light: #34d399;
    --error-color: #ef4444;
    --error-hover: #dc2626;
    --error-light: #f87171;
    --warning-color: #f59e0b;
    --warning-light: #fbbf24;

    /* Bordures */
    --border-color: rgba(255, 255, 255, 0.1);
    --border-light: rgba(255, 255, 255, 0.2);
    --border-accent: rgba(99, 102, 241, 0.3);

    /* Ombres */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.6);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.7);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);

    /* Rayons de bordure */
    --radius-xs: 4px;
    --radius-sm: 6px;
    --radius: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 20px;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: all 0.15s ease;
    --transition: all 0.2s ease;
    --transition-slow: all 0.3s ease;
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Typographie */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', monospace;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;

    /* Espacements */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
    height: 100vh;
    font-size: var(--font-size-base);
}

/* Layout principal */
.app-container {
    display: flex;
    height: 100vh;
    position: relative;
    backdrop-filter: blur(10px);
}

/* Sidebar gauche */
.sidebar {
    width: 300px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: relative;
    z-index: 100;
}

.sidebar.hidden {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

.sidebar-header h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.sidebar-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.history-list {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
}

.history-list::-webkit-scrollbar {
    width: 6px;
}

.history-list::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.history-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
}

/* Zone principale */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    position: relative;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.chat-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.chat-header h1 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.chat-header p {
    font-size: 14px;
    color: var(--text-secondary);
}

/* Zone des messages */
.messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px;
    scroll-behavior: smooth;
}

.messages::-webkit-scrollbar {
    width: 8px;
}

.messages::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

.messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
}

/* Zone de saisie */
.input-container {
    padding: 20px 24px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.attachment-preview {
    display: none;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--bg-tertiary);
    border-radius: var(--radius);
    margin-bottom: 12px;
    font-size: 14px;
}

.attachment-preview.visible {
    display: flex;
}

.file-name {
    flex: 1;
    color: var(--text-secondary);
}

.remove-attachment {
    cursor: pointer;
    color: var(--error-color);
    font-weight: bold;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.remove-attachment:hover {
    background: var(--error-color);
    color: white;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 12px;
    transition: var(--transition);
}

.input-wrapper:focus-within {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.message-input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-size: 16px;
    line-height: 1.5;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    font-family: var(--font-family);
}

.message-input::placeholder {
    color: var(--text-muted);
}

.file-input {
    display: none;
}

/* Boutons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: var(--radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--accent-hover);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: var(--success-hover);
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: var(--error-hover);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-icon {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.btn-icon:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.attachment-button {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--radius);
    transition: var(--transition);
    font-size: 16px;
}

.attachment-button:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.send-button {
    background: var(--accent-color);
    border: none;
    color: white;
    cursor: pointer;
    padding: 10px;
    border-radius: var(--radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-button:hover {
    background: var(--accent-hover);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Toggle buttons */
.toggle-sidebar,
.toggle-right-panel {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--radius);
    transition: var(--transition);
    z-index: 200;
}

.toggle-sidebar {
    right: -40px;
}

.toggle-right-panel {
    left: -40px;
}

.toggle-sidebar:hover,
.toggle-right-panel:hover {
    background: var(--accent-color);
    color: white;
}

/* États vides */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.empty-state p {
    margin-bottom: 16px;
    font-size: 14px;
}

/* Utilitaires */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success-color); }
.text-error { color: var(--error-color); }
.text-warning { color: var(--warning-color); }

.bg-primary { background: var(--bg-primary); }
.bg-secondary { background: var(--bg-secondary); }
.bg-tertiary { background: var(--bg-tertiary); }

.hidden { display: none !important; }
.visible { display: block !important; }

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        z-index: 1000;
    }
    
    .sidebar.hidden {
        transform: translateX(-100%);
    }
    
    .toggle-sidebar {
        display: block;
    }
}
