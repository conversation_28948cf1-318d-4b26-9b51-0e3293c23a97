/**
 * Styles des composants - Interface moderne et élégante
 */

/* ===== SIDEBAR ===== */
.sidebar {
    width: 320px;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: relative;
    z-index: 100;
    box-shadow: var(--shadow);
}

.sidebar.hidden {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-glass-hover);
    backdrop-filter: blur(30px);
}

.sidebar-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
    color: var(--text-primary);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-actions {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.history-list {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-4);
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.history-list::-webkit-scrollbar {
    width: 6px;
}

.history-list::-webkit-scrollbar-track {
    background: transparent;
}

.history-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-full);
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
}

/* ===== CHAT ITEMS ===== */
.chat-item {
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-3);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.chat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--accent-gradient);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.chat-item:hover {
    border-color: var(--border-accent);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.chat-item:hover::before {
    opacity: 0.1;
}

.chat-item.active {
    border-color: var(--accent-color);
    background: var(--bg-glass-hover);
    box-shadow: var(--shadow-glow);
}

.chat-item.active::before {
    opacity: 0.15;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-2);
}

.chat-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.4;
    flex: 1;
    margin-right: var(--space-2);
}

.chat-actions {
    display: flex;
    gap: var(--space-1);
    opacity: 0;
    transition: var(--transition);
}

.chat-item:hover .chat-actions {
    opacity: 1;
}

.chat-preview {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--space-2);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.chat-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.message-count {
    background: var(--bg-tertiary);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-weight: 500;
}

.chat-timestamp {
    font-weight: 500;
}

/* ===== ZONE PRINCIPALE ===== */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    position: relative;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.chat-header {
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-glass-hover);
    backdrop-filter: blur(30px);
    position: relative;
}

.chat-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--accent-gradient);
    opacity: 0.3;
}

.chat-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin-bottom: var(--space-2);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
}

.chat-header p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== MESSAGES ===== */
.messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-8);
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.messages::-webkit-scrollbar {
    width: 8px;
}

.messages::-webkit-scrollbar-track {
    background: transparent;
}

.messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-full);
}

.messages::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
}

.message {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
}

.message.user .message-avatar {
    background: var(--accent-gradient);
    border-color: var(--accent-color);
    box-shadow: var(--shadow-glow);
}

.message.assistant .message-avatar {
    background: var(--bg-tertiary);
    border-color: var(--success-color);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
}

.message-content {
    flex: 1;
    max-width: calc(100% - 60px);
}

.message.user .message-content {
    text-align: right;
}

.message-bubble {
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--space-4) var(--space-5);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow);
    position: relative;
    word-wrap: break-word;
    line-height: 1.6;
}

.message.user .message-bubble {
    background: var(--accent-gradient);
    border-color: var(--accent-color);
    color: white;
    box-shadow: var(--shadow-glow);
}

.message.assistant .message-bubble {
    background: var(--bg-glass-hover);
    border-color: var(--border-light);
}

.message-timestamp {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-top: var(--space-2);
    opacity: 0.7;
}

.message.user .message-timestamp {
    color: rgba(255, 255, 255, 0.8);
}

/* ===== INDICATEUR DE FRAPPE ===== */
.typing-indicator {
    display: none;
    align-items: center;
    gap: var(--space-4);
    padding: 0 var(--space-8) var(--space-4);
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.typing-dots {
    display: flex;
    gap: var(--space-1);
    padding: var(--space-3) var(--space-4);
    background: var(--bg-glass);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-muted);
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* ===== ZONE DE SAISIE ===== */
.input-container {
    padding: var(--space-6) var(--space-8);
    border-top: 1px solid var(--border-color);
    background: var(--bg-glass-hover);
    backdrop-filter: blur(30px);
    position: relative;
}

.input-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--accent-gradient);
    opacity: 0.3;
}

.attachment-preview {
    display: none;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
    font-size: var(--font-size-sm);
    backdrop-filter: blur(10px);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.attachment-preview.visible {
    display: flex;
}

.file-name {
    flex: 1;
    color: var(--text-secondary);
    font-weight: 500;
}

.remove-attachment {
    cursor: pointer;
    color: var(--error-color);
    font-weight: bold;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    transition: var(--transition);
    font-size: var(--font-size-sm);
}

.remove-attachment:hover {
    background: var(--error-color);
    color: white;
    transform: scale(1.1);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: var(--space-3);
    background: var(--bg-glass);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--space-4);
    transition: var(--transition);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--accent-gradient);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.input-wrapper:focus-within {
    border-color: var(--accent-color);
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
}

.input-wrapper:focus-within::before {
    opacity: 0.1;
}

.message-input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    line-height: 1.5;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    font-family: var(--font-family);
    font-weight: 500;
    padding: var(--space-1) 0;
}

.message-input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

.file-input {
    display: none;
}

.attachment-button {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    transition: var(--transition);
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.attachment-button:hover {
    background: var(--bg-tertiary);
    color: var(--accent-color);
    transform: scale(1.1);
}

.send-button {
    background: var(--accent-gradient);
    border: none;
    color: white;
    cursor: pointer;
    padding: var(--space-3);
    border-radius: var(--radius-xl);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    box-shadow: var(--shadow-glow);
    position: relative;
    overflow: hidden;
}

.send-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%);
    opacity: 0;
    transition: var(--transition);
}

.send-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.5);
}

.send-button:hover::before {
    opacity: 1;
}

.send-button:active {
    transform: scale(0.95);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.send-button:disabled:hover {
    transform: none;
    box-shadow: var(--shadow-glow);
}
