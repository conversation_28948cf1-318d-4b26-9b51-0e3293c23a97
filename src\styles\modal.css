/**
 * Styles des modals et panneaux - Interface moderne
 */

/* ===== PANNEAU DROIT ===== */
.right-panel {
    width: 400px;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: relative;
    z-index: 100;
    box-shadow: var(--shadow);
}

.right-panel.hidden-right {
    transform: translateX(100%);
}

.right-panel-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-glass-hover);
    backdrop-filter: blur(30px);
}

.right-panel-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-6);
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.panel-content::-webkit-scrollbar {
    width: 6px;
}

.panel-content::-webkit-scrollbar-track {
    background: transparent;
}

.panel-content::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-full);
}

.panel-content::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
}

.panel-section {
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    margin-bottom: var(--space-4);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.panel-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.panel-actions {
    display: flex;
    gap: var(--space-2);
}

.image-preview {
    width: 100%;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.image-preview:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-color);
}

.image-info {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--space-3);
    text-align: center;
    font-weight: 500;
}

.html-preview {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    padding: var(--space-4);
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.html-preview::-webkit-scrollbar {
    width: 4px;
}

.html-preview::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-full);
}

.html-render {
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    padding: var(--space-4);
    margin-top: var(--space-4);
    background: white;
    color: black;
    max-height: 300px;
    overflow-y: auto;
}

.warning-text {
    color: var(--error-light);
    font-size: var(--font-size-sm);
    margin-top: var(--space-3);
    font-weight: 500;
}

.generic-content {
    color: var(--text-secondary);
}

.generic-content p {
    margin-bottom: var(--space-2);
}

.content-preview {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    padding: var(--space-3);
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
    margin-top: var(--space-3);
    white-space: pre-wrap;
    word-break: break-all;
}

/* ===== MODAL PLEIN ÉCRAN ===== */
.fullscreen-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(10px);
    }
}

.fullscreen-content {
    max-width: 95vw;
    max-height: 95vh;
    position: relative;
    animation: modalZoomIn 0.3s ease-out;
}

@keyframes modalZoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.fullscreen-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 2px solid var(--border-light);
}

.fullscreen-close {
    position: absolute;
    top: -60px;
    right: 0;
    background: var(--error-color);
    color: white;
    border: none;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 600;
    transition: var(--transition);
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
}

.fullscreen-close:hover {
    background: var(--error-hover);
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.fullscreen-download {
    position: absolute;
    top: -60px;
    right: 100px;
    background: var(--success-color);
    color: white;
    border: none;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 600;
    transition: var(--transition);
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
}

.fullscreen-download:hover {
    background: var(--success-hover);
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

/* ===== PIÈCES JOINTES DANS LES MESSAGES ===== */
.message-attachment {
    margin-top: var(--space-3);
}

.image-container {
    position: relative;
    display: inline-block;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.attachment-image {
    max-width: 250px;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition);
    display: block;
}

.attachment-image:hover {
    transform: scale(1.02);
}

.fullscreen-btn {
    position: absolute;
    top: var(--space-2);
    right: var(--space-2);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius);
    font-size: var(--font-size-sm);
    cursor: pointer;
    opacity: 0;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.image-container:hover .fullscreen-btn {
    opacity: 1;
}

.fullscreen-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.html-attachment {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition);
    display: inline-block;
    color: var(--text-secondary);
    font-weight: 500;
}

.html-attachment:hover {
    background: var(--bg-quaternary);
    border-color: var(--accent-color);
    color: var(--accent-light);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

/* ===== BOUTONS TOGGLE ===== */
.toggle-sidebar,
.toggle-right-panel {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    transition: var(--transition);
    z-index: 200;
    box-shadow: var(--shadow);
}

.toggle-sidebar {
    right: -50px;
}

.toggle-right-panel {
    left: -50px;
}

.toggle-sidebar:hover,
.toggle-right-panel:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-glow);
}

/* ===== ÉTATS VIDES ===== */
.empty-state {
    text-align: center;
    padding: var(--space-16) var(--space-8);
    color: var(--text-secondary);
}

.empty-state p {
    margin-bottom: var(--space-4);
    font-size: var(--font-size-base);
    font-weight: 500;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .right-panel {
        width: 100%;
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        z-index: 1000;
    }
    
    .fullscreen-close,
    .fullscreen-download {
        top: var(--space-4);
        position: fixed;
    }
    
    .fullscreen-close {
        right: var(--space-4);
    }
    
    .fullscreen-download {
        right: calc(var(--space-4) + 80px);
    }
}
