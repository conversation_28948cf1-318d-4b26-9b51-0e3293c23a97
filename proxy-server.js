const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
const PORT = 3001;

// Configuration CORS
app.use(cors({
    origin: '*', // ou spécifiez votre domaine
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Accept']
}));

app.use(express.json());

// Route de test
app.post('/webhook-proxy/test', (req, res) => {
    console.log('Test reçu:', req.body);
    res.json({
        reply: `Echo: ${req.body.message || 'Message vide'}`,
        timestamp: new Date().toISOString(),
        status: 'success'
    });
});

// Route proxy pour n8n - Version simplifiée
app.post('/webhook-proxy/analyse', async (req, res) => {
    try {
        console.log('=== REQUÊTE REÇUE ===');
        console.log('Body reçu:', req.body);
        console.log('Type:', typeof req.body);

        // S'assurer que le body est un objet JSON valide
        const jsonPayload = req.body;
        console.log('JSON à envoyer:', jsonPayload);

        const response = await fetch('https://n8n.arkeup.com/webhook/analyse', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(jsonPayload)
        });

        console.log('=== RÉPONSE N8N ===');
        console.log('Status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Erreur n8n:', errorText);
            return res.status(response.status).json({
                error: `Erreur n8n: ${response.status}`,
                details: errorText
            });
        }

        // Vérifier le type de contenu
        const contentType = response.headers.get('content-type') || '';
        console.log('Content-Type:', contentType);

        let data;

        if (contentType.includes('image/')) {
            // C'est une image, la convertir en base64
            console.log('🖼️ Image détectée, conversion en base64');
            const buffer = await response.arrayBuffer();
            const base64 = Buffer.from(buffer).toString('base64');
            const dataUrl = `data:${contentType};base64,${base64}`;

            data = {
                reply: '🖼️ Graphique généré',
                image: dataUrl,
                type: 'image'
            };
        } else {
            // Traitement normal pour JSON/texte
            const textData = await response.text();
            console.log('Réponse brute:', textData);

            if (textData.trim() === '') {
                data = { reply: 'Webhook exécuté avec succès' };
            } else {
                try {
                    const parsedData = JSON.parse(textData);

                    // Vérifier si la réponse JSON contient une image corrompue
                    if (parsedData.reply && parsedData.reply.startsWith('�PNG')) {
                        console.log('🖼️ Image PNG corrompue détectée dans JSON');
                        data = {
                            reply: '🖼️ Image détectée mais corrompue. Veuillez configurer n8n pour retourner l\'image en base64.',
                            error: 'Image binaire corrompue en JSON'
                        };
                    } else {
                        data = parsedData;
                    }
                } catch (jsonError) {
                    data = { reply: textData };
                }
            }
        }

        console.log('Données finales:', data);
        res.json(data);
    } catch (error) {
        console.error('Erreur proxy:', error);
        res.status(500).json({ error: 'Erreur du proxy', details: error.message });
    }
});

app.listen(PORT, () => {
    console.log(`Proxy server running on http://localhost:${PORT}`);
});
