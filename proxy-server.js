const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
const PORT = 3001;

// Configuration CORS
app.use(cors({
    origin: '*', // ou spécifiez votre domaine
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Accept']
}));

app.use(express.json());

// Route de test
app.post('/webhook-proxy/test', (req, res) => {
    console.log('Test reçu:', req.body);
    res.json({
        reply: `Echo: ${req.body.message || 'Message vide'}`,
        timestamp: new Date().toISOString(),
        status: 'success'
    });
});

// Route proxy pour n8n
app.post('/webhook-proxy/analyse', async (req, res) => {
    try {
        console.log('Requête vers n8n:', req.body);

        const response = await fetch('https://n8n.arkeup.com/webhook-test/analyse', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(req.body)
        });

        console.log('Réponse n8n status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Erreur n8n:', errorText);
            return res.status(response.status).json({
                error: `Erreur n8n: ${response.status}`,
                details: errorText
            });
        }

        // Gérer les réponses non-JSON
        const contentType = response.headers.get('content-type');
        let data;

        try {
            // Essayer d'abord de lire comme texte
            const textData = await response.text();
            console.log('Réponse brute de n8n:', textData);

            if (textData.trim() === '') {
                data = { reply: 'Webhook exécuté avec succès (réponse vide)' };
            } else if (contentType && contentType.includes('application/json')) {
                try {
                    data = JSON.parse(textData);
                } catch (jsonError) {
                    console.error('Erreur parsing JSON:', jsonError);
                    data = { reply: textData };
                }
            } else {
                data = { reply: textData };
            }
        } catch (readError) {
            console.error('Erreur lecture réponse:', readError);
            data = { reply: 'Erreur lors de la lecture de la réponse' };
        }

        console.log('Données n8n:', data);
        res.json(data);
    } catch (error) {
        console.error('Erreur proxy:', error);
        res.status(500).json({ error: 'Erreur du proxy', details: error.message });
    }
});

app.listen(PORT, () => {
    console.log(`Proxy server running on http://localhost:${PORT}`);
});
