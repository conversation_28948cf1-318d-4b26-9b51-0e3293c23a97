const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
const PORT = 3001;

// Configuration CORS
app.use(cors({
    origin: '*', // ou spécifiez votre domaine
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Accept']
}));

app.use(express.json());

// Route de test
app.post('/webhook-proxy/test', (req, res) => {
    console.log('Test reçu:', req.body);
    res.json({
        reply: `Echo: ${req.body.message || 'Message vide'}`,
        timestamp: new Date().toISOString(),
        status: 'success'
    });
});

// Route proxy pour n8n
app.post('/webhook-proxy/analyse', async (req, res) => {
    try {
        console.log('Requête vers n8n:', req.body);

        const response = await fetch('https://n8n.arkeup.com/webhook-test/analyse', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(req.body)
        });

        console.log('Réponse n8n status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Erreur n8n:', errorText);
            return res.status(response.status).json({
                error: `Erreur n8n: ${response.status}`,
                details: errorText
            });
        }

        const data = await response.json();
        console.log('Données n8n:', data);
        res.json(data);
    } catch (error) {
        console.error('Erreur proxy:', error);
        res.status(500).json({ error: 'Erreur du proxy', details: error.message });
    }
});

app.listen(PORT, () => {
    console.log(`Proxy server running on http://localhost:${PORT}`);
});
