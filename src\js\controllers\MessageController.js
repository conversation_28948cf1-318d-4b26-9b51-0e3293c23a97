/**
 * Contrôleur de messages - Gère l'affichage et l'envoi des messages
 * Principe Single Responsibility: Responsable uniquement de la gestion des messages
 */
class MessageController {
    constructor(appController) {
        this.app = appController;
    }

    /**
     * Envoie un message
     */
    async sendMessage() {
        const input = document.getElementById('message-input');
        const text = input?.value?.trim();
        
        if (!text) {
            console.log('⚠️ Message vide, envoi annulé');
            return;
        }

        try {
            this.app.setState(APP_STATES.SENDING);
            
            // Créer le message utilisateur
            const userMessage = new Message(text, MESSAGE_ROLES.USER);
            
            // Ajouter une pièce jointe si présente
            if (this.app.attachmentController && this.app.attachmentController.currentAttachment) {
                userMessage.setAttachment(this.app.attachmentController.currentAttachment);
                this.app.attachmentController.removeAttachment();
            }
            
            // Ajouter le message au chat actuel
            const currentChat = this.app.getCurrentChat();
            if (!currentChat) {
                console.error('❌ Aucun chat actuel');
                return;
            }
            
            currentChat.addMessage(userMessage);
            
            // Vider l'input et rendre les messages
            input.value = '';
            this.autoResizeTextarea(input);
            this.renderMessages();
            this.showTypingIndicator();
            
            // Émettre un événement
            document.dispatchEvent(new CustomEvent(EVENTS.MESSAGE_SENT, {
                detail: { messageId: userMessage.id, content: text }
            }));
            
            // Envoyer au webhook
            this.app.setState(APP_STATES.RECEIVING);
            const response = await this.app.webhookService.sendMessage(text, userMessage.attachment);
            
            // Traiter la réponse
            const processedResponse = this.app.responseProcessor.processResponse(response);
            
            // Créer le message de réponse
            const assistantMessage = new Message(
                processedResponse.content, 
                MESSAGE_ROLES.ASSISTANT,
                processedResponse.attachment
            );
            
            currentChat.addMessage(assistantMessage);
            
            // Gérer les pièces jointes dans la réponse
            if (processedResponse.attachment) {
                this.handleResponseAttachment(processedResponse.attachment, response);
            }
            
            // Rendre les messages et cacher l'indicateur
            this.hideTypingIndicator();
            this.renderMessages();
            
            // Sauvegarder
            this.app.saveData();
            
            // Émettre un événement
            document.dispatchEvent(new CustomEvent(EVENTS.MESSAGE_RECEIVED, {
                detail: { messageId: assistantMessage.id, content: processedResponse.content }
            }));
            
            this.app.setState(APP_STATES.IDLE);
            
        } catch (error) {
            console.error('❌ Erreur envoi message:', error);
            this.hideTypingIndicator();
            
            // Créer un message d'erreur
            const errorMessage = new Message(
                this.formatErrorMessage(error),
                MESSAGE_ROLES.ASSISTANT
            );
            
            const currentChat = this.app.getCurrentChat();
            if (currentChat) {
                currentChat.addMessage(errorMessage);
                this.renderMessages();
                this.app.saveData();
            }
            
            this.app.setState(APP_STATES.ERROR);
        }
    }

    /**
     * Rend tous les messages du chat actuel
     */
    renderMessages() {
        const container = document.getElementById('messages');
        if (!container) {
            console.error('❌ Conteneur messages non trouvé');
            return;
        }

        container.innerHTML = '';
        
        const currentChat = this.app.getCurrentChat();
        if (!currentChat) {
            console.log('📭 Aucun chat actuel à rendre');
            return;
        }

        currentChat.messages.forEach(message => {
            const messageElement = this.createMessageElement(message);
            container.appendChild(messageElement);
        });

        // Scroll vers le bas
        container.scrollTop = container.scrollHeight;
        
        console.log('🎨 Messages rendus:', currentChat.messages.length);
    }

    /**
     * Crée un élément DOM pour un message
     * @param {Message} message - Le message
     * @returns {HTMLElement}
     */
    createMessageElement(message) {
        const div = document.createElement('div');
        div.className = `message ${message.role}`;
        div.dataset.messageId = message.id;

        // Créer l'avatar
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        avatarDiv.textContent = message.getAvatar();

        // Créer le contenu
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        // Vérifier si le contenu contient du HTML dangereux
        const content = message.content || '';
        if (this.isDangerousContent(content)) {
            contentDiv.textContent = `⚠️ Contenu HTML neutralisé: ${content}`;
        } else {
            contentDiv.innerHTML = this.formatContent(content);
        }

        // Ajouter timestamp pour les messages assistant
        if (message.isAssistantMessage()) {
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-timestamp';
            timeDiv.textContent = this.formatTimestamp(message.timestamp);
            contentDiv.appendChild(timeDiv);
        }

        div.appendChild(avatarDiv);
        div.appendChild(contentDiv);

        // Gérer les pièces jointes
        if (message.hasAttachment()) {
            const attachmentElement = this.createAttachmentElement(message.attachment, message.id);
            contentDiv.appendChild(attachmentElement);
        }

        return div;
    }

    /**
     * Crée un élément pour une pièce jointe
     * @param {Object} attachment - La pièce jointe
     * @param {string} messageId - L'ID du message
     * @returns {HTMLElement}
     */
    createAttachmentElement(attachment, messageId) {
        const div = document.createElement('div');
        div.className = 'message-attachment';

        if (attachment.type === CONTENT_TYPES.IMAGE) {
            const imageId = `msg-image-${messageId}`;
            div.innerHTML = `
                <div class="image-container">
                    <img id="${imageId}" src="${attachment.data}" 
                         class="attachment-image" 
                         onclick="modalController.displayInRightPanel(${JSON.stringify(attachment).replace(/"/g, '&quot;')})"
                         title="Cliquer pour voir dans le panneau">
                    <button onclick="modalController.openFullscreen('${imageId}')" 
                            class="fullscreen-btn" 
                            title="Plein écran">🔍</button>
                </div>
            `;
        } else if (attachment.type === CONTENT_TYPES.HTML) {
            div.innerHTML = `
                <div class="html-attachment" 
                     onclick="modalController.displayInRightPanel(${JSON.stringify(attachment).replace(/"/g, '&quot;')})">
                    📄 ${attachment.fileName || 'Code HTML'}
                </div>
            `;
        }

        return div;
    }

    /**
     * Formate le contenu d'un message
     * @param {string} content - Le contenu à formater
     * @returns {string}
     */
    formatContent(content) {
        if (!content) return '';

        // Échapper le HTML pour éviter l'injection
        let formatted = content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');

        // Appliquer le formatage markdown
        formatted = formatted
            .replace(/\n/g, '<br>')
            .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="message-link">$1</a>')
            .replace(/```([\s\S]*?)```/g, '<pre class="code-block">$1</pre>')
            .replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');

        return formatted;
    }

    /**
     * Vérifie si le contenu est dangereux
     * @param {string} content - Le contenu à vérifier
     * @returns {boolean}
     */
    isDangerousContent(content) {
        return content && (
            content.includes('<iframe') ||
            content.includes('<script') ||
            content.includes('<object') ||
            content.includes('<embed')
        );
    }

    /**
     * Formate un timestamp
     * @param {Date} timestamp - Le timestamp
     * @returns {string}
     */
    formatTimestamp(timestamp) {
        return new Date(timestamp).toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Gère les pièces jointes dans les réponses
     * @param {Object} attachment - La pièce jointe
     * @param {Object} originalResponse - La réponse originale
     */
    handleResponseAttachment(attachment, originalResponse) {
        if (attachment.type === CONTENT_TYPES.IMAGE) {
            // Afficher dans le panneau droit
            if (this.app.modalController) {
                this.app.modalController.displayInRightPanel(attachment);
            }
        }
    }

    /**
     * Formate un message d'erreur
     * @param {Error} error - L'erreur
     * @returns {string}
     */
    formatErrorMessage(error) {
        const message = error.message || 'Erreur inconnue';
        
        if (message.includes('Failed to fetch')) {
            return CONFIG.ERROR_MESSAGES.FAILED_TO_FETCH;
        } else if (message.includes('CORS')) {
            return CONFIG.ERROR_MESSAGES.CORS_ERROR;
        } else if (message.includes('404')) {
            return CONFIG.ERROR_MESSAGES.NOT_FOUND;
        } else if (message.includes('500')) {
            return CONFIG.ERROR_MESSAGES.SERVER_ERROR;
        }
        
        return `⚠️ Erreur : ${message}`;
    }

    /**
     * Affiche l'indicateur de frappe
     */
    showTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.style.display = 'flex';
        }
    }

    /**
     * Cache l'indicateur de frappe
     */
    hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    /**
     * Auto-redimensionne un textarea
     * @param {HTMLTextAreaElement} textarea - Le textarea
     */
    autoResizeTextarea(textarea) {
        if (!textarea) return;
        
        textarea.style.height = 'auto';
        const newHeight = Math.min(textarea.scrollHeight, CONFIG.MAX_MESSAGE_HEIGHT);
        textarea.style.height = newHeight + 'px';
    }
}
