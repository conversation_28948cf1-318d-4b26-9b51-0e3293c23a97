<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #0a0a0a; color: #e6e6e6; }
        .message { margin: 10px 0; padding: 10px; background: #111; border-radius: 8px; }
        .user { background: #1a1a2e; }
        .assistant { background: #16213e; }
        input { width: 300px; padding: 10px; margin: 10px 0; }
        button { padding: 10px 20px; background: #6366f1; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test Simple Chat</h1>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="Tapez votre message...">
    <button onclick="sendMessage()">Envoyer</button>

    <script>
        let messages = [];

        function renderMessages() {
            const container = document.getElementById('messages');
            container.innerHTML = '';
            messages.forEach(msg => {
                const div = document.createElement('div');
                div.className = 'message ' + msg.role;
                div.textContent = msg.content;
                container.appendChild(div);
            });
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const text = input.value.trim();
            if (!text) return;

            // Ajouter message utilisateur
            messages.push({ role: 'user', content: text });
            renderMessages();
            input.value = '';

            try {
                const response = await fetch('http://localhost:3001/webhook-proxy/analyse', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: text })
                });

                const data = await response.json();
                messages.push({ role: 'assistant', content: data.reply || 'Réponse reçue' });
                renderMessages();
            } catch (error) {
                messages.push({ role: 'assistant', content: 'Erreur: ' + error.message });
                renderMessages();
            }
        }

        // Permettre l'envoi avec Entrée
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        console.log('Test simple chargé');
    </script>
</body>
</html>
