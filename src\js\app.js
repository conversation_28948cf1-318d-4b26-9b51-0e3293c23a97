/**
 * Point d'entrée principal de l'application
 * Initialise et coordonne tous les composants
 */

// Variables globales pour l'accès depuis le HTML
let appController;
let chatController;
let messageController;
let attachmentController;
let uiController;
let modalController;
let webhookService;

/**
 * Initialisation de l'application
 */
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('🚀 Démarrage de l\'application...');
        
        // Créer le contrôleur principal
        appController = new AppController();
        
        // Initialiser l'application
        await appController.initialize();
        
        // Exposer les contrôleurs pour l'accès depuis le HTML
        chatController = appController.chatController;
        messageController = appController.messageController;
        attachmentController = appController.attachmentController;
        uiController = appController.uiController;
        modalController = appController.modalController;
        webhookService = appController.webhookService;
        
        // Ajouter des méthodes globales pour la compatibilité
        window.chatController = chatController;
        window.messageController = messageController;
        window.attachmentController = attachmentController;
        window.uiController = uiController;
        window.modalController = modalController;
        window.appController = appController;
        
        // Méthodes de compatibilité pour les anciens appels
        window.newChat = () => chatController.createNewChat();
        window.loadChat = (id) => chatController.loadChat(id);
        window.sendMessage = () => messageController.sendMessage();
        window.toggleSidebar = () => uiController.toggleSidebar();
        window.toggleRightPanel = () => uiController.toggleRightPanel();
        window.resetApp = () => appController.resetApp();
        window.testWebhook = () => webhookService.testConnection().then(result => {
            alert(`${result.message}\nStatus: ${result.status || 'N/A'}\nDonnées: ${JSON.stringify(result.data || {}, null, 2)}`);
        });
        
        console.log('✅ Application démarrée avec succès');
        
    } catch (error) {
        console.error('❌ Erreur critique lors du démarrage:', error);
        
        // Mode de récupération d'urgence
        document.body.innerHTML = `
            <div style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100vh;
                background: #0a0a0a;
                color: #e6e6e6;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                text-align: center;
                padding: 20px;
            ">
                <div>
                    <h1 style="color: #ef4444; margin-bottom: 16px;">⚠️ Erreur de démarrage</h1>
                    <p style="margin-bottom: 20px;">L'application n'a pas pu se charger correctement.</p>
                    <p style="font-size: 14px; color: #a0a0a0; margin-bottom: 20px;">
                        Erreur: ${error.message}
                    </p>
                    <button onclick="location.reload()" style="
                        background: #6366f1;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 14px;
                    ">
                        Recharger la page
                    </button>
                </div>
            </div>
        `;
    }
});

/**
 * Gestion des erreurs globales
 */
window.addEventListener('error', (event) => {
    console.error('❌ Erreur JavaScript globale:', event.error);
    
    // Envoyer l'erreur à un service de monitoring si configuré
    if (typeof reportError === 'function') {
        reportError(event.error);
    }
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('❌ Promise rejetée non gérée:', event.reason);
    
    // Empêcher l'affichage de l'erreur dans la console du navigateur
    event.preventDefault();
    
    // Envoyer l'erreur à un service de monitoring si configuré
    if (typeof reportError === 'function') {
        reportError(event.reason);
    }
});

/**
 * Gestion de la visibilité de la page
 */
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('📱 Application mise en arrière-plan');
        // Sauvegarder les données avant la mise en arrière-plan
        if (appController) {
            appController.saveData();
        }
    } else {
        console.log('📱 Application remise au premier plan');
        // Optionnel: vérifier les mises à jour ou synchroniser
    }
});

/**
 * Gestion du redimensionnement de la fenêtre
 */
window.addEventListener('resize', () => {
    // Ajuster l'interface si nécessaire
    if (uiController) {
        uiController.handleResize();
    }
});

/**
 * Raccourcis clavier globaux
 */
document.addEventListener('keydown', (event) => {
    // Ctrl/Cmd + N : Nouveau chat
    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault();
        if (chatController) {
            chatController.createNewChat();
        }
    }
    
    // Ctrl/Cmd + / : Toggle sidebar
    if ((event.ctrlKey || event.metaKey) && event.key === '/') {
        event.preventDefault();
        if (uiController) {
            uiController.toggleSidebar();
        }
    }
    
    // Escape : Fermer les modals
    if (event.key === 'Escape') {
        if (modalController) {
            modalController.closeFullscreen();
        }
    }
    
    // Ctrl/Cmd + R : Reset (avec confirmation)
    if ((event.ctrlKey || event.metaKey) && event.key === 'r' && event.shiftKey) {
        event.preventDefault();
        if (appController) {
            appController.resetApp();
        }
    }
});

/**
 * Fonctions utilitaires globales
 */
window.AppUtils = {
    /**
     * Formate une date de manière lisible
     */
    formatDate: (date) => {
        return new Intl.DateTimeFormat('fr-FR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    },
    
    /**
     * Copie du texte dans le presse-papiers
     */
    copyToClipboard: async (text) => {
        try {
            await navigator.clipboard.writeText(text);
            console.log('📋 Texte copié dans le presse-papiers');
            return true;
        } catch (error) {
            console.error('❌ Erreur copie presse-papiers:', error);
            return false;
        }
    },
    
    /**
     * Télécharge un fichier
     */
    downloadFile: (content, filename, mimeType = 'text/plain') => {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    },
    
    /**
     * Valide une URL
     */
    isValidUrl: (string) => {
        try {
            new URL(string);
            return true;
        } catch {
            return false;
        }
    },
    
    /**
     * Génère un ID unique
     */
    generateId: () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    /**
     * Debounce une fonction
     */
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

/**
 * Informations de version et debug
 */
window.AppInfo = {
    version: '2.0.0',
    buildDate: new Date().toISOString(),
    environment: 'development',
    
    getDebugInfo: () => {
        return {
            version: window.AppInfo.version,
            buildDate: window.AppInfo.buildDate,
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString(),
            chatsCount: appController ? appController.chats.length : 0,
            currentChatId: appController ? appController.currentChatId : null,
            storageSize: appController ? appController.storageService.getDataSize() : 0
        };
    }
};

console.log('📋 Application Info:', window.AppInfo.getDebugInfo());
