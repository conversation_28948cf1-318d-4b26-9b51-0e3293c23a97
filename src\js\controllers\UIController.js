/**
 * Contrôleur UI - Gère l'interface utilisateur
 * Principe Single Responsibility: Responsable uniquement de l'UI
 */
class UIController {
    constructor(appController) {
        this.app = appController;
        this.sidebarVisible = true;
        this.rightPanelVisible = true;
    }

    /**
     * Toggle la sidebar
     */
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        this.sidebarVisible = !this.sidebarVisible;
        sidebar.classList.toggle('hidden', !this.sidebarVisible);
        
        console.log('📱 Sidebar:', this.sidebarVisible ? 'visible' : 'cachée');
    }

    /**
     * Toggle le panneau droit
     */
    toggleRightPanel() {
        const rightPanel = document.getElementById('right-panel');
        if (!rightPanel) return;

        this.rightPanelVisible = !this.rightPanelVisible;
        rightPanel.classList.toggle('hidden-right', !this.rightPanelVisible);
        
        console.log('📱 Panneau droit:', this.rightPanelVisible ? 'visible' : 'caché');
    }

    /**
     * Gère le redimensionnement de la fenêtre
     */
    handleResize() {
        const width = window.innerWidth;
        
        // Sur mobile, cacher automatiquement la sidebar
        if (width < 768) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar && this.sidebarVisible) {
                this.toggleSidebar();
            }
        }
        
        console.log('📱 Redimensionnement:', width + 'px');
    }

    /**
     * Affiche une notification
     * @param {string} message - Le message
     * @param {string} type - Le type (success, error, warning, info)
     * @param {number} duration - Durée en ms
     */
    showNotification(message, type = 'info', duration = 3000) {
        // Créer l'élément de notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Styles inline pour la notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        // Couleurs selon le type
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#6366f1'
        };
        
        notification.style.backgroundColor = colors[type] || colors.info;
        
        // Ajouter au DOM
        document.body.appendChild(notification);
        
        // Animation d'entrée
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Suppression automatique
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
        
        console.log(`📢 Notification ${type}:`, message);
    }

    /**
     * Affiche un loader
     * @param {string} message - Message du loader
     */
    showLoader(message = 'Chargement...') {
        this.hideLoader(); // Supprimer l'ancien loader s'il existe
        
        const loader = document.createElement('div');
        loader.id = 'app-loader';
        loader.innerHTML = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                color: white;
                font-family: var(--font-family);
            ">
                <div style="text-align: center;">
                    <div style="
                        width: 40px;
                        height: 40px;
                        border: 3px solid #333;
                        border-top: 3px solid #6366f1;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 16px;
                    "></div>
                    <p>${message}</p>
                </div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
        
        document.body.appendChild(loader);
    }

    /**
     * Cache le loader
     */
    hideLoader() {
        const loader = document.getElementById('app-loader');
        if (loader) {
            loader.remove();
        }
    }

    /**
     * Affiche une boîte de dialogue de confirmation
     * @param {string} message - Le message
     * @param {string} title - Le titre
     * @returns {Promise<boolean>}
     */
    async showConfirmDialog(message, title = 'Confirmation') {
        return new Promise((resolve) => {
            const dialog = document.createElement('div');
            dialog.innerHTML = `
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100vw;
                    height: 100vh;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    font-family: var(--font-family);
                ">
                    <div style="
                        background: var(--bg-secondary);
                        border: 1px solid var(--border-color);
                        border-radius: 12px;
                        padding: 24px;
                        max-width: 400px;
                        width: 90%;
                        color: var(--text-primary);
                    ">
                        <h3 style="margin: 0 0 16px 0; color: var(--text-primary);">${title}</h3>
                        <p style="margin: 0 0 24px 0; color: var(--text-secondary);">${message}</p>
                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button id="dialog-cancel" style="
                                background: var(--bg-tertiary);
                                color: var(--text-primary);
                                border: 1px solid var(--border-color);
                                padding: 8px 16px;
                                border-radius: 6px;
                                cursor: pointer;
                            ">Annuler</button>
                            <button id="dialog-confirm" style="
                                background: var(--accent-color);
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                cursor: pointer;
                            ">Confirmer</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(dialog);
            
            // Gestionnaires d'événements
            dialog.querySelector('#dialog-confirm').onclick = () => {
                dialog.remove();
                resolve(true);
            };
            
            dialog.querySelector('#dialog-cancel').onclick = () => {
                dialog.remove();
                resolve(false);
            };
            
            // Fermer avec Escape
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    dialog.remove();
                    document.removeEventListener('keydown', handleEscape);
                    resolve(false);
                }
            };
            
            document.addEventListener('keydown', handleEscape);
        });
    }

    /**
     * Met à jour le titre de la page
     * @param {string} title - Le nouveau titre
     */
    updatePageTitle(title) {
        document.title = title;
    }

    /**
     * Scroll vers un élément
     * @param {string} elementId - L'ID de l'élément
     * @param {string} behavior - Le comportement du scroll
     */
    scrollToElement(elementId, behavior = 'smooth') {
        const element = document.getElementById(elementId);
        if (element) {
            element.scrollIntoView({ behavior, block: 'nearest' });
        }
    }

    /**
     * Focus sur un élément
     * @param {string} elementId - L'ID de l'élément
     */
    focusElement(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.focus();
        }
    }

    /**
     * Applique un thème
     * @param {string} theme - Le nom du thème
     */
    applyTheme(theme) {
        document.body.className = `theme-${theme}`;
        localStorage.setItem('app-theme', theme);
        console.log('🎨 Thème appliqué:', theme);
    }

    /**
     * Charge le thème sauvegardé
     */
    loadSavedTheme() {
        const savedTheme = localStorage.getItem('app-theme');
        if (savedTheme) {
            this.applyTheme(savedTheme);
        }
    }

    /**
     * Retourne les informations sur l'écran
     * @returns {Object}
     */
    getScreenInfo() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            isMobile: window.innerWidth < 768,
            isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
            isDesktop: window.innerWidth >= 1024
        };
    }
}
