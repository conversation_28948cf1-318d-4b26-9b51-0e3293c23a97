/**
 * <PERSON><PERSON><PERSON><PERSON> - Représente une conversation
 * Principe Single Responsibility: Gère uniquement les données d'un chat
 */
class Chat {
    constructor(id = null, title = 'Nouvelle conversation') {
        this.id = id || Date.now().toString();
        this.title = title;
        this.messages = [];
        this.timestamp = new Date();
        this.isActive = false;
    }

    /**
     * Ajoute un message au chat
     * @param {Message} message - Le message à ajouter
     */
    addMessage(message) {
        if (!(message instanceof Message)) {
            throw new Error('Le paramètre doit être une instance de Message');
        }
        
        this.messages.push(message);
        this.updateTimestamp();
        
        // Mettre à jour le titre si c'est le premier message utilisateur
        if (this.messages.length === 1 && message.role === MESSAGE_ROLES.USER) {
            this.updateTitle(message.content);
        }
    }

    /**
     * Met à jour le titre du chat basé sur le contenu
     * @param {string} content - Le contenu pour générer le titre
     */
    updateTitle(content) {
        if (!content || typeof content !== 'string') return;
        
        const maxLength = 50;
        this.title = content.length > maxLength 
            ? content.slice(0, maxLength) + '…' 
            : content;
    }

    /**
     * Met à jour le timestamp
     */
    updateTimestamp() {
        this.timestamp = new Date();
    }

    /**
     * Retourne le dernier message
     * @returns {Message|null}
     */
    getLastMessage() {
        return this.messages.length > 0 ? this.messages[this.messages.length - 1] : null;
    }

    /**
     * Retourne les messages d'un rôle spécifique
     * @param {string} role - Le rôle (user/assistant)
     * @returns {Message[]}
     */
    getMessagesByRole(role) {
        return this.messages.filter(message => message.role === role);
    }

    /**
     * Compte le nombre de messages
     * @returns {number}
     */
    getMessageCount() {
        return this.messages.length;
    }

    /**
     * Vérifie si le chat est vide
     * @returns {boolean}
     */
    isEmpty() {
        return this.messages.length === 0;
    }

    /**
     * Active le chat
     */
    activate() {
        this.isActive = true;
    }

    /**
     * Désactive le chat
     */
    deactivate() {
        this.isActive = false;
    }

    /**
     * Sérialise le chat pour le stockage
     * @returns {Object}
     */
    toJSON() {
        return {
            id: this.id,
            title: this.title,
            messages: this.messages.map(msg => msg.toJSON()),
            timestamp: this.timestamp.toISOString(),
            isActive: this.isActive
        };
    }

    /**
     * Crée un chat à partir de données sérialisées
     * @param {Object} data - Les données sérialisées
     * @returns {Chat}
     */
    static fromJSON(data) {
        const chat = new Chat(data.id, data.title);
        chat.timestamp = new Date(data.timestamp);
        chat.isActive = data.isActive || false;
        
        // Reconstruire les messages
        if (data.messages && Array.isArray(data.messages)) {
            chat.messages = data.messages.map(msgData => Message.fromJSON(msgData));
        }
        
        return chat;
    }

    /**
     * Valide les données du chat
     * @returns {boolean}
     */
    isValid() {
        return this.id && 
               typeof this.title === 'string' && 
               Array.isArray(this.messages) &&
               this.timestamp instanceof Date;
    }
}
